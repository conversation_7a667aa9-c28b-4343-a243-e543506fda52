import torch
from torch import nn

"""https://ojs.aaai.org/index.php/AAAI/article/view/27852
由于计算机视觉的快速发展，近年来单模态（RGB）目标跟踪取得了长足的进步。
考虑到单一成像传感器的局限性，引入多模态图像（RGB、红外等）来弥补这一缺陷，用于复杂环境下的全天候目标跟踪。
然而，由于获取足够的多模态跟踪数据困难，同时主导模态随开放环境的变化而变化，现有的大多数技术无法动态提取多模态互补信息，
导致跟踪性能不理想。针对这一问题，我们提出了一种基于通用双向适配器的多模态视觉提示跟踪模型，该模型可以相互交叉提示多种模态。
我们的模型由一个通用双向适配器(Bi_direct_adapter)和多个具有共享参数的模态特定变压器编码器分支组成。
编码器通过使用冻结的预训练基础模型分别提取每种模态的特征。
我们开发了一种简单而有效的光特征适配器，用于将模态特定信息从一种模态传递到另一种模态，以自适应方式执行视觉特征提示融合。
通过添加较少（0.32M）的可训练参数，与完全微调方法和基于提示学习的方法相比，我们的模型获得了更优越的跟踪性能。
"""

class QuickGELU(nn.Module):
    def forward(self, x: torch.Tensor):
        return x * torch.sigmoid(1.702 * x)


class Bi_direct_adapter(nn.Module):
    def __init__(self, dim=8, xavier_init=False):
        super().__init__()

        self.adapter_down = nn.Linear(768, dim)  
        self.adapter_up = nn.Linear(dim, 768)  
        self.adapter_mid = nn.Linear(dim, dim)

        #nn.init.xavier_uniform_(self.adapter_down.weight)
        nn.init.zeros_(self.adapter_mid.bias)
        nn.init.zeros_(self.adapter_mid.weight)
        nn.init.zeros_(self.adapter_down.weight)
        nn.init.zeros_(self.adapter_down.bias)
        nn.init.zeros_(self.adapter_up.weight)
        nn.init.zeros_(self.adapter_up.bias)

        #self.act = QuickGELU()
        self.dropout = nn.Dropout(0.1)
        self.dim = dim

    def forward(self, x):
        x_down = self.adapter_down(x)
        x_down = self.adapter_mid(x_down)
        x_down = self.dropout(x_down)
        x_up = self.adapter_up(x_down)
        return x_up

if __name__ == '__main__':
    batch_size = 4
    num_features = 768

    block = Bi_direct_adapter(dim=8)

    input = torch.rand(batch_size, 1, num_features)

    output = block(input)

    # 打印输入和输出的尺寸
    print(f'Input RGB size: {input.size()}')
    print(f'Output RGB size: {output.size()}')
