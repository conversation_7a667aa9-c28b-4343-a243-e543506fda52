import torch
import torch.nn as nn


"""ICCV2021<Score-Based Point Cloud Denoising>
点云从扫描设备获取时常常受到噪声的干扰，这会影响诸如表面重建和分析等下游任务。可以将有噪声的点云的分布视为一组无噪声样本的分布与某种噪声模型卷积的结果，其模式即为其底层的干净表面。
为了对有噪声的点云进行去噪，我们提出通过梯度上升法增加每个点的对数似然——即通过迭代更新每个点的位置。由于在测试时噪声分布是未知的，并且我们只需要分数（即对数概率函数的梯度）来执行梯度上升，
因此我们提出了一种神经网络架构，用于估计给定有噪点云时的分数。我们推导了用于训练网络的目标函数，并开发了基于估计分数的去噪算法。
"""

class ResnetBlockConv1d(nn.Module):
    """ 1D-Convolutional ResNet block class.
    Args:
        size_in (int): input dimension
        size_out (int): output dimension
        size_h (int): hidden dimension
    """

    def __init__(self, c_dim, size_in, size_h=None, size_out=None,
                 norm_method='batch_norm', legacy=False):
        super().__init__()
        # Attributes
        if size_h is None:
            size_h = size_in
        if size_out is None:
            size_out = size_in

        self.size_in = size_in
        self.size_h = size_h
        self.size_out = size_out
        # Submodules
        if norm_method == 'batch_norm':
            norm = nn.BatchNorm1d
        elif norm_method == 'sync_batch_norm':
            norm = nn.SyncBatchNorm
        else:
             raise Exception("Invalid norm method: %s" % norm_method)

        self.bn_0 = norm(size_in)
        self.bn_1 = norm(size_h)

        self.fc_0 = nn.Conv1d(size_in, size_h, 1)
        self.fc_1 = nn.Conv1d(size_h, size_out, 1)
        self.fc_c = nn.Conv1d(c_dim, size_out, 1)
        self.actvn = nn.ReLU()

        if size_in == size_out:
            self.shortcut = None
        else:
            self.shortcut = nn.Conv1d(size_in, size_out, 1, bias=False)

        # Initialization
        nn.init.zeros_(self.fc_1.weight)

    def forward(self, x, c):
        net = self.fc_0(self.actvn(self.bn_0(x)))
        dx = self.fc_1(self.actvn(self.bn_1(net)))

        if self.shortcut is not None:
            x_s = self.shortcut(x)
        else:
            x_s = x

        out = x_s + dx + self.fc_c(c)

        return out


class ScoreNet(nn.Module):

    def __init__(self, z_dim, dim, out_dim, hidden_size, num_blocks):
        """
        Args:
            z_dim:   Dimension of context vectors.
            dim:     Point dimension.
            out_dim: Gradient dim.
            hidden_size:   Hidden states dim.
        """
        super().__init__()
        self.z_dim = z_dim
        self.dim = dim
        self.out_dim = out_dim
        self.hidden_size = hidden_size
        self.num_blocks = num_blocks

        # Input = Conditional = zdim (code) + dim (xyz)
        c_dim = z_dim + dim
        self.conv_p = nn.Conv1d(c_dim, hidden_size, 1)
        self.blocks = nn.ModuleList([
            ResnetBlockConv1d(c_dim, hidden_size) for _ in range(num_blocks)
        ])
        self.bn_out = nn.BatchNorm1d(hidden_size)
        self.conv_out = nn.Conv1d(hidden_size, out_dim, 1)
        self.actvn_out = nn.ReLU()

    def forward(self, x, c):
        """
        :param x: (bs, npoints, self.dim) Input coordinate (xyz)
        :param c: (bs, self.zdim) Shape latent code
        :return: (bs, npoints, self.dim) Gradient (self.dim dimension)
        """
        p = x.transpose(1, 2)  # (bs, dim, n_points)
        batch_size, D, num_points = p.size()

        c_expand = c.unsqueeze(2).expand(-1, -1, num_points)
        c_xyz = torch.cat([p, c_expand], dim=1)
        net = self.conv_p(c_xyz)
        for block in self.blocks:
            net = block(net, c_xyz)
        out = self.conv_out(self.actvn_out(self.bn_out(net))).transpose(1, 2)
        return out

if __name__ == '__main__':
    z_dim = 32  # 潜在代码（latent code）的维度
    dim = 3  # 点的维度
    out_dim = 3  # 输出的维度
    hidden_size = 64  # 隐藏层的维度
    num_blocks = 5  # 残差块（ResNet Block）的数量

    block = ScoreNet(z_dim=z_dim, dim=dim, out_dim=out_dim, hidden_size=hidden_size, num_blocks=num_blocks)

    # batch_size=8, npoints=100, dim=3
    x = torch.rand(8, 100, dim)  # (批次大小, 点数量, 维度)
    c = torch.rand(8, z_dim)  # (批次大小, 潜在代码维度)

    output = block(x, c)

    print("输入形状:", x.size())
    print("条件输入形状:", c.size())
    print("输出形状:", output.size())
