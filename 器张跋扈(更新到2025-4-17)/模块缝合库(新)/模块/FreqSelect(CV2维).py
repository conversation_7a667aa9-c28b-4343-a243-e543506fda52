import torch
import torch.nn as nn

"""CVPR2024
扩张卷积通过在连续元素之间插入间隙来扩大感受野，广泛用于计算机视觉。在这项研究中，我们从谱分析的角度提出了三种策略来改进扩张卷积的各个阶段。
与将全局膨胀率固定为超参数的传统做法不同，我们引入了频率自适应膨胀卷积 （FADC），它根据局部频率分量在空间上动态调整膨胀率。随后，我们设计了两个插件模块，以直接提高有效带宽和感受野大小。
Adaptive Kernel （AdaKern） 模块将卷积权重分解为低频和高频分量，并按通道动态调整这些分量之间的比率。通过增加卷积权重的高频部分，AdaKern 捕获了更多的高频分量，从而提高了有效带宽。
频率选择 （FreqSelect） 模块通过空间变化重新加权来优化平衡特征表示中的高频和低频分量。它抑制了背景中的高频，以鼓励 FADC 学习更大的膨胀，从而增加感受野以扩大范围。
关于分割和对象检测的广泛实验始终验证了我们方法的有效性。
"""

class FrequencySelection(nn.Module):
    def __init__(self, in_channels=3, k_list=[2], lowfreq_att=True, fs_feat='feat', lp_type='avgpool',
                 act='sigmoid', spatial='conv', spatial_group=1, spatial_kernel=3, init='zero',
                 global_selection=False):
        super().__init__()
        self.k_list = k_list
        self.lp_list = nn.ModuleList()
        self.freq_weight_conv_list = nn.ModuleList()
        self.fs_feat = fs_feat
        self.lp_type = lp_type
        self.in_channels = in_channels
        self.spatial_group = spatial_group
        self.lowfreq_att = lowfreq_att

        if spatial == 'conv':
            _n = len(k_list)
            if lowfreq_att: _n += 1
            for i in range(_n):
                freq_weight_conv = nn.Conv2d(in_channels=in_channels,
                                             out_channels=self.spatial_group,
                                             stride=1,
                                             kernel_size=spatial_kernel,
                                             groups=self.spatial_group,
                                             padding=spatial_kernel // 2,
                                             bias=True)
                if init == 'zero':
                    freq_weight_conv.weight.data.zero_()
                    freq_weight_conv.bias.data.zero_()
                self.freq_weight_conv_list.append(freq_weight_conv)

        if self.lp_type == 'avgpool':
            for k in k_list:
                self.lp_list.append(nn.Sequential(
                    nn.ReplicationPad2d(padding=k // 2),
                    nn.AvgPool2d(kernel_size=k, padding=0, stride=1)
                ))

        self.act = act
        self.global_selection = global_selection

    def sp_act(self, freq_weight):
        if self.act == 'sigmoid':
            freq_weight = freq_weight.sigmoid() * 2
        elif self.act == 'softmax':
            freq_weight = freq_weight.softmax(dim=1) * freq_weight.shape[1]
        return freq_weight

    def forward(self, x, att_feat=None):
        if att_feat is None: att_feat = x
        x_list = []
        if self.lp_type == 'avgpool':
            pre_x = x
            b, _, h, w = x.shape
            for idx, avg in enumerate(self.lp_list):
                low_part = avg(x)

                # Resize low_part to match pre_x dimensions
                if low_part.shape[2:] != pre_x.shape[2:]:
                    low_part = nn.functional.interpolate(low_part, size=(h, w), mode='bilinear', align_corners=False)

                high_part = pre_x - low_part
                pre_x = low_part
                freq_weight = self.freq_weight_conv_list[idx](att_feat)
                freq_weight = self.sp_act(freq_weight)
                tmp = freq_weight.reshape(b, self.spatial_group, -1, h, w) * high_part.reshape(b, self.spatial_group,
                                                                                               -1, h, w)
                x_list.append(tmp.reshape(b, -1, h, w))

            if self.lowfreq_att:
                freq_weight = self.freq_weight_conv_list[len(x_list)](att_feat)
                tmp = freq_weight.reshape(b, self.spatial_group, -1, h, w) * pre_x.reshape(b, self.spatial_group, -1, h,
                                                                                           w)
                x_list.append(tmp.reshape(b, -1, h, w))
            else:
                x_list.append(pre_x)

        if len(x_list) == 0:
            raise ValueError("x_list is empty, check your forward logic.")

        x = torch.cat(x_list, dim=1)
        return x


if __name__ == '__main__':
    block = FrequencySelection(in_channels=3)
    input_tensor = torch.rand(32, 3, 64, 64)
    output = block(input_tensor)
    print("Input size:", input_tensor.size())
    print("Output size:", output.size())
