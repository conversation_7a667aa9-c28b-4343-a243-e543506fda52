import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import math

from einops import rearrange


"""《FSTA-SNN:Frequency-based Spatial-Temporal Attention Module for Spiking Neural Networks》AAAI 2025
脉冲神经网络 (SNN) 因其固有的节能特性而成为人工神经网络 (ANN) 的有前途的替代品。由于 SNN 中脉冲生成固有的稀疏性，中间输出脉冲的深入分析和优化经常被忽视。
这种疏忽严重限制了 SNN 固有的能源效率，并削弱了它们在时空特征提取方面的优势，导致准确性不足和不必要的能源消耗。在这项工作中，我们从时间和空间的角度分析了 SNN 固有的脉冲特性。
在空间分析方面，我们发现浅层倾向于专注于学习特征的垂直变化，而较深的层则逐渐学习特征的水平变化。关于时间分析，我们观察到不同时间步骤之间的特征学习没有显着差异。这表明增加时间步骤对特征学习的影响有限。
基于从这些分析中得出的见解，我们提出了一个基于频率的时空注意 (FSTA) 模块来增强 SNN 中的特征学习。该模块旨在通过抑制冗余脉冲来提高特征学习能力。
实验结果表明，FSTA 模块的引入显著降低了 SNN 的脉冲发放率，与多个数据集上最先进的基线相比表现出了更优异的性能。
"""
# B站：箫张跋扈 整理并修改(https://space.bilibili.com/478113245)

class DCT8x8(nn.Module):
    def __init__(self):
        super(DCT8x8, self).__init__()
        self.filter = torch.zeros(64, 8, 8).cuda()
        self.freq_num = 64
        self.freq_range = 8

        self.filter[0] = torch.Tensor([[0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250]])

        self.filter[1] = torch.Tensor([[0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734],
                                       [0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470],
                                       [0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982],
                                       [0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345],
                                       [-0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345],
                                       [-0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982],
                                       [-0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470],
                                       [-0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734]])

        self.filter[2] = torch.Tensor([[0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633],
                                       [0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676],
                                       [-0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676],
                                       [-0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633],
                                       [-0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633],
                                       [-0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676],
                                       [0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676],
                                       [0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633]])

        self.filter[3] = torch.Tensor([[0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470],
                                       [-0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345],
                                       [-0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734],
                                       [-0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982],
                                       [0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982],
                                       [0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734],
                                       [0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345],
                                       [-0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470]])

        self.filter[4] = torch.Tensor([[0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [-0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250],
                                       [-0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250],
                                       [-0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250],
                                       [-0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250, -0.1250],
                                       [0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250, 0.1250]])

        self.filter[5] = torch.Tensor([[0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982],
                                       [-0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734],
                                       [0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345],
                                       [0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470],
                                       [-0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470],
                                       [-0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345],
                                       [0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734],
                                       [-0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982]])

        self.filter[6] = torch.Tensor([[0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676],
                                       [-0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633],
                                       [0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633],
                                       [-0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676],
                                       [-0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676, -0.0676],
                                       [0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633, 0.1633],
                                       [-0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633, -0.1633],
                                       [0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676, 0.0676]])

        self.filter[7] = torch.Tensor([[0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345, 0.0345],
                                       [-0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982, -0.0982],
                                       [0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470, 0.1470],
                                       [-0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734, -0.1734],
                                       [0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734, 0.1734],
                                       [-0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470, -0.1470],
                                       [0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982, 0.0982],
                                       [-0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345, -0.0345]])

        self.filter[8] = torch.Tensor([[0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                       [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                       [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                       [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                       [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                       [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                       [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                       [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734]])
        self.filter[9] = torch.Tensor([[0.2405, 0.2039, 0.1362, 0.0478, -0.0478, -0.1362, -0.2039, -0.2405],
                                       [0.2039, 0.1728, 0.1155, 0.0406, -0.0406, -0.1155, -0.1728, -0.2039],
                                       [0.1362, 0.1155, 0.0772, 0.0271, -0.0271, -0.0772, -0.1155, -0.1362],
                                       [0.0478, 0.0406, 0.0271, 0.0095, -0.0095, -0.0271, -0.0406, -0.0478],
                                       [-0.0478, -0.0406, -0.0271, -0.0095, 0.0095, 0.0271, 0.0406, 0.0478],
                                       [-0.1362, -0.1155, -0.0772, -0.0271, 0.0271, 0.0772, 0.1155, 0.1362],
                                       [-0.2039, -0.1728, -0.1155, -0.0406, 0.0406, 0.1155, 0.1728, 0.2039],
                                       [-0.2405, -0.2039, -0.1362, -0.0478, 0.0478, 0.1362, 0.2039, 0.2405]])
        self.filter[10] = torch.Tensor([[0.2265, 0.1920, 0.1283, 0.0451, -0.0451, -0.1283, -0.1920, -0.2265],
                                        [0.0938, 0.0795, 0.0532, 0.0187, -0.0187, -0.0532, -0.0795, -0.0938],
                                        [-0.0938, -0.0795, -0.0532, -0.0187, 0.0187, 0.0532, 0.0795, 0.0938],
                                        [-0.2265, -0.1920, -0.1283, -0.0451, 0.0451, 0.1283, 0.1920, 0.2265],
                                        [-0.2265, -0.1920, -0.1283, -0.0451, 0.0451, 0.1283, 0.1920, 0.2265],
                                        [-0.0938, -0.0795, -0.0532, -0.0187, 0.0187, 0.0532, 0.0795, 0.0938],
                                        [0.0938, 0.0795, 0.0532, 0.0187, -0.0187, -0.0532, -0.0795, -0.0938],
                                        [0.2265, 0.1920, 0.1283, 0.0451, -0.0451, -0.1283, -0.1920, -0.2265]])
        self.filter[11] = torch.Tensor([[0.2039, 0.1728, 0.1155, 0.0406, -0.0406, -0.1155, -0.1728, -0.2039],
                                        [-0.0478, -0.0406, -0.0271, -0.0095, 0.0095, 0.0271, 0.0406, 0.0478],
                                        [-0.2405, -0.2039, -0.1362, -0.0478, 0.0478, 0.1362, 0.2039, 0.2405],
                                        [-0.1362, -0.1155, -0.0772, -0.0271, 0.0271, 0.0772, 0.1155, 0.1362],
                                        [0.1362, 0.1155, 0.0772, 0.0271, -0.0271, -0.0772, -0.1155, -0.1362],
                                        [0.2405, 0.2039, 0.1362, 0.0478, -0.0478, -0.1362, -0.2039, -0.2405],
                                        [0.0478, 0.0406, 0.0271, 0.0095, -0.0095, -0.0271, -0.0406, -0.0478],
                                        [-0.2039, -0.1728, -0.1155, -0.0406, 0.0406, 0.1155, 0.1728, 0.2039]])
        self.filter[12] = torch.Tensor([[0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                        [-0.1734, -0.1470, -0.0982, -0.0345, 0.0345, 0.0982, 0.1470, 0.1734],
                                        [-0.1734, -0.1470, -0.0982, -0.0345, 0.0345, 0.0982, 0.1470, 0.1734],
                                        [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                        [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734],
                                        [-0.1734, -0.1470, -0.0982, -0.0345, 0.0345, 0.0982, 0.1470, 0.1734],
                                        [-0.1734, -0.1470, -0.0982, -0.0345, 0.0345, 0.0982, 0.1470, 0.1734],
                                        [0.1734, 0.1470, 0.0982, 0.0345, -0.0345, -0.0982, -0.1470, -0.1734]])
        self.filter[13] = torch.Tensor([[0.1362, 0.1155, 0.0772, 0.0271, -0.0271, -0.0772, -0.1155, -0.1362],
                                        [-0.2405, -0.2039, -0.1362, -0.0478, 0.0478, 0.1362, 0.2039, 0.2405],
                                        [0.0478, 0.0406, 0.0271, 0.0095, -0.0095, -0.0271, -0.0406, -0.0478],
                                        [0.2039, 0.1728, 0.1155, 0.0406, -0.0406, -0.1155, -0.1728, -0.2039],
                                        [-0.2039, -0.1728, -0.1155, -0.0406, 0.0406, 0.1155, 0.1728, 0.2039],
                                        [-0.0478, -0.0406, -0.0271, -0.0095, 0.0095, 0.0271, 0.0406, 0.0478],
                                        [0.2405, 0.2039, 0.1362, 0.0478, -0.0478, -0.1362, -0.2039, -0.2405],
                                        [-0.1362, -0.1155, -0.0772, -0.0271, 0.0271, 0.0772, 0.1155, 0.1362]])
        self.filter[14] = torch.Tensor([[0.0938, 0.0795, 0.0532, 0.0187, -0.0187, -0.0532, -0.0795, -0.0938],
                                        [-0.2265, -0.1920, -0.1283, -0.0451, 0.0451, 0.1283, 0.1920, 0.2265],
                                        [0.2265, 0.1920, 0.1283, 0.0451, -0.0451, -0.1283, -0.1920, -0.2265],
                                        [-0.0938, -0.0795, -0.0532, -0.0187, 0.0187, 0.0532, 0.0795, 0.0938],
                                        [-0.0938, -0.0795, -0.0532, -0.0187, 0.0187, 0.0532, 0.0795, 0.0938],
                                        [0.2265, 0.1920, 0.1283, 0.0451, -0.0451, -0.1283, -0.1920, -0.2265],
                                        [-0.2265, -0.1920, -0.1283, -0.0451, 0.0451, 0.1283, 0.1920, 0.2265],
                                        [0.0938, 0.0795, 0.0532, 0.0187, -0.0187, -0.0532, -0.0795, -0.0938]])
        self.filter[15] = torch.Tensor([[0.0478, 0.0406, 0.0271, 0.0095, -0.0095, -0.0271, -0.0406, -0.0478],
                                        [-0.1362, -0.1155, -0.0772, -0.0271, 0.0271, 0.0772, 0.1155, 0.1362],
                                        [0.2039, 0.1728, 0.1155, 0.0406, -0.0406, -0.1155, -0.1728, -0.2039],
                                        [-0.2405, -0.2039, -0.1362, -0.0478, 0.0478, 0.1362, 0.2039, 0.2405],
                                        [0.2405, 0.2039, 0.1362, 0.0478, -0.0478, -0.1362, -0.2039, -0.2405],
                                        [-0.2039, -0.1728, -0.1155, -0.0406, 0.0406, 0.1155, 0.1728, 0.2039],
                                        [0.1362, 0.1155, 0.0772, 0.0271, -0.0271, -0.0772, -0.1155, -0.1362],
                                        [-0.0478, -0.0406, -0.0271, -0.0095, 0.0095, 0.0271, 0.0406, 0.0478]])
        self.filter[16] = torch.Tensor([[0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633]])
        self.filter[17] = torch.Tensor([[0.2265, 0.0938, -0.0938, -0.2265, -0.2265, -0.0938, 0.0938, 0.2265],
                                        [0.1920, 0.0795, -0.0795, -0.1920, -0.1920, -0.0795, 0.0795, 0.1920],
                                        [0.1283, 0.0532, -0.0532, -0.1283, -0.1283, -0.0532, 0.0532, 0.1283],
                                        [0.0451, 0.0187, -0.0187, -0.0451, -0.0451, -0.0187, 0.0187, 0.0451],
                                        [-0.0451, -0.0187, 0.0187, 0.0451, 0.0451, 0.0187, -0.0187, -0.0451],
                                        [-0.1283, -0.0532, 0.0532, 0.1283, 0.1283, 0.0532, -0.0532, -0.1283],
                                        [-0.1920, -0.0795, 0.0795, 0.1920, 0.1920, 0.0795, -0.0795, -0.1920],
                                        [-0.2265, -0.0938, 0.0938, 0.2265, 0.2265, 0.0938, -0.0938, -0.2265]])
        self.filter[18] = torch.Tensor([[0.2134, 0.0884, -0.0884, -0.2134, -0.2134, -0.0884, 0.0884, 0.2134],
                                        [0.0884, 0.0366, -0.0366, -0.0884, -0.0884, -0.0366, 0.0366, 0.0884],
                                        [-0.0884, -0.0366, 0.0366, 0.0884, 0.0884, 0.0366, -0.0366, -0.0884],
                                        [-0.2134, -0.0884, 0.0884, 0.2134, 0.2134, 0.0884, -0.0884, -0.2134],
                                        [-0.2134, -0.0884, 0.0884, 0.2134, 0.2134, 0.0884, -0.0884, -0.2134],
                                        [-0.0884, -0.0366, 0.0366, 0.0884, 0.0884, 0.0366, -0.0366, -0.0884],
                                        [0.0884, 0.0366, -0.0366, -0.0884, -0.0884, -0.0366, 0.0366, 0.0884],
                                        [0.2134, 0.0884, -0.0884, -0.2134, -0.2134, -0.0884, 0.0884, 0.2134]])
        self.filter[19] = torch.Tensor([[0.1920, 0.0795, -0.0795, -0.1920, -0.1920, -0.0795, 0.0795, 0.1920],
                                        [-0.0451, -0.0187, 0.0187, 0.0451, 0.0451, 0.0187, -0.0187, -0.0451],
                                        [-0.2265, -0.0938, 0.0938, 0.2265, 0.2265, 0.0938, -0.0938, -0.2265],
                                        [-0.1283, -0.0532, 0.0532, 0.1283, 0.1283, 0.0532, -0.0532, -0.1283],
                                        [0.1283, 0.0532, -0.0532, -0.1283, -0.1283, -0.0532, 0.0532, 0.1283],
                                        [0.2265, 0.0938, -0.0938, -0.2265, -0.2265, -0.0938, 0.0938, 0.2265],
                                        [0.0451, 0.0187, -0.0187, -0.0451, -0.0451, -0.0187, 0.0187, 0.0451],
                                        [-0.1920, -0.0795, 0.0795, 0.1920, 0.1920, 0.0795, -0.0795, -0.1920]])
        self.filter[20] = torch.Tensor([[0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [-0.1633, -0.0676, 0.0676, 0.1633, 0.1633, 0.0676, -0.0676, -0.1633],
                                        [-0.1633, -0.0676, 0.0676, 0.1633, 0.1633, 0.0676, -0.0676, -0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633],
                                        [-0.1633, -0.0676, 0.0676, 0.1633, 0.1633, 0.0676, -0.0676, -0.1633],
                                        [-0.1633, -0.0676, 0.0676, 0.1633, 0.1633, 0.0676, -0.0676, -0.1633],
                                        [0.1633, 0.0676, -0.0676, -0.1633, -0.1633, -0.0676, 0.0676, 0.1633]])
        self.filter[21] = torch.Tensor([[0.1283, 0.0532, -0.0532, -0.1283, -0.1283, -0.0532, 0.0532, 0.1283],
                                        [-0.2265, -0.0938, 0.0938, 0.2265, 0.2265, 0.0938, -0.0938, -0.2265],
                                        [0.0451, 0.0187, -0.0187, -0.0451, -0.0451, -0.0187, 0.0187, 0.0451],
                                        [0.1920, 0.0795, -0.0795, -0.1920, -0.1920, -0.0795, 0.0795, 0.1920],
                                        [-0.1920, -0.0795, 0.0795, 0.1920, 0.1920, 0.0795, -0.0795, -0.1920],
                                        [-0.0451, -0.0187, 0.0187, 0.0451, 0.0451, 0.0187, -0.0187, -0.0451],
                                        [0.2265, 0.0938, -0.0938, -0.2265, -0.2265, -0.0938, 0.0938, 0.2265],
                                        [-0.1283, -0.0532, 0.0532, 0.1283, 0.1283, 0.0532, -0.0532, -0.1283]])
        self.filter[22] = torch.Tensor([[0.0884, 0.0366, -0.0366, -0.0884, -0.0884, -0.0366, 0.0366, 0.0884],
                                        [-0.2134, -0.0884, 0.0884, 0.2134, 0.2134, 0.0884, -0.0884, -0.2134],
                                        [0.2134, 0.0884, -0.0884, -0.2134, -0.2134, -0.0884, 0.0884, 0.2134],
                                        [-0.0884, -0.0366, 0.0366, 0.0884, 0.0884, 0.0366, -0.0366, -0.0884],
                                        [-0.0884, -0.0366, 0.0366, 0.0884, 0.0884, 0.0366, -0.0366, -0.0884],
                                        [0.2134, 0.0884, -0.0884, -0.2134, -0.2134, -0.0884, 0.0884, 0.2134],
                                        [-0.2134, -0.0884, 0.0884, 0.2134, 0.2134, 0.0884, -0.0884, -0.2134],
                                        [0.0884, 0.0366, -0.0366, -0.0884, -0.0884, -0.0366, 0.0366, 0.0884]])
        self.filter[23] = torch.Tensor([[0.0451, 0.0187, -0.0187, -0.0451, -0.0451, -0.0187, 0.0187, 0.0451],
                                        [-0.1283, -0.0532, 0.0532, 0.1283, 0.1283, 0.0532, -0.0532, -0.1283],
                                        [0.1920, 0.0795, -0.0795, -0.1920, -0.1920, -0.0795, 0.0795, 0.1920],
                                        [-0.2265, -0.0938, 0.0938, 0.2265, 0.2265, 0.0938, -0.0938, -0.2265],
                                        [0.2265, 0.0938, -0.0938, -0.2265, -0.2265, -0.0938, 0.0938, 0.2265],
                                        [-0.1920, -0.0795, 0.0795, 0.1920, 0.1920, 0.0795, -0.0795, -0.1920],
                                        [0.1283, 0.0532, -0.0532, -0.1283, -0.1283, -0.0532, 0.0532, 0.1283],
                                        [-0.0451, -0.0187, 0.0187, 0.0451, 0.0451, 0.0187, -0.0187, -0.0451]])
        self.filter[24] = torch.Tensor([[0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470]])
        self.filter[25] = torch.Tensor([[0.2039, -0.0478, -0.2405, -0.1362, 0.1362, 0.2405, 0.0478, -0.2039],
                                        [0.1728, -0.0406, -0.2039, -0.1155, 0.1155, 0.2039, 0.0406, -0.1728],
                                        [0.1155, -0.0271, -0.1362, -0.0772, 0.0772, 0.1362, 0.0271, -0.1155],
                                        [0.0406, -0.0095, -0.0478, -0.0271, 0.0271, 0.0478, 0.0095, -0.0406],
                                        [-0.0406, 0.0095, 0.0478, 0.0271, -0.0271, -0.0478, -0.0095, 0.0406],
                                        [-0.1155, 0.0271, 0.1362, 0.0772, -0.0772, -0.1362, -0.0271, 0.1155],
                                        [-0.1728, 0.0406, 0.2039, 0.1155, -0.1155, -0.2039, -0.0406, 0.1728],
                                        [-0.2039, 0.0478, 0.2405, 0.1362, -0.1362, -0.2405, -0.0478, 0.2039]])
        self.filter[26] = torch.Tensor([[0.1920, -0.0451, -0.2265, -0.1283, 0.1283, 0.2265, 0.0451, -0.1920],
                                        [0.0795, -0.0187, -0.0938, -0.0532, 0.0532, 0.0938, 0.0187, -0.0795],
                                        [-0.0795, 0.0187, 0.0938, 0.0532, -0.0532, -0.0938, -0.0187, 0.0795],
                                        [-0.1920, 0.0451, 0.2265, 0.1283, -0.1283, -0.2265, -0.0451, 0.1920],
                                        [-0.1920, 0.0451, 0.2265, 0.1283, -0.1283, -0.2265, -0.0451, 0.1920],
                                        [-0.0795, 0.0187, 0.0938, 0.0532, -0.0532, -0.0938, -0.0187, 0.0795],
                                        [0.0795, -0.0187, -0.0938, -0.0532, 0.0532, 0.0938, 0.0187, -0.0795],
                                        [0.1920, -0.0451, -0.2265, -0.1283, 0.1283, 0.2265, 0.0451, -0.1920]])
        self.filter[27] = torch.Tensor([[0.1728, -0.0406, -0.2039, -0.1155, 0.1155, 0.2039, 0.0406, -0.1728],
                                        [-0.0406, 0.0095, 0.0478, 0.0271, -0.0271, -0.0478, -0.0095, 0.0406],
                                        [-0.2039, 0.0478, 0.2405, 0.1362, -0.1362, -0.2405, -0.0478, 0.2039],
                                        [-0.1155, 0.0271, 0.1362, 0.0772, -0.0772, -0.1362, -0.0271, 0.1155],
                                        [0.1155, -0.0271, -0.1362, -0.0772, 0.0772, 0.1362, 0.0271, -0.1155],
                                        [0.2039, -0.0478, -0.2405, -0.1362, 0.1362, 0.2405, 0.0478, -0.2039],
                                        [0.0406, -0.0095, -0.0478, -0.0271, 0.0271, 0.0478, 0.0095, -0.0406],
                                        [-0.1728, 0.0406, 0.2039, 0.1155, -0.1155, -0.2039, -0.0406, 0.1728]])
        self.filter[28] = torch.Tensor([[0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [-0.1470, 0.0345, 0.1734, 0.0982, -0.0982, -0.1734, -0.0345, 0.1470],
                                        [-0.1470, 0.0345, 0.1734, 0.0982, -0.0982, -0.1734, -0.0345, 0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470],
                                        [-0.1470, 0.0345, 0.1734, 0.0982, -0.0982, -0.1734, -0.0345, 0.1470],
                                        [-0.1470, 0.0345, 0.1734, 0.0982, -0.0982, -0.1734, -0.0345, 0.1470],
                                        [0.1470, -0.0345, -0.1734, -0.0982, 0.0982, 0.1734, 0.0345, -0.1470]])
        self.filter[29] = torch.Tensor([[0.1155, -0.0271, -0.1362, -0.0772, 0.0772, 0.1362, 0.0271, -0.1155],
                                        [-0.2039, 0.0478, 0.2405, 0.1362, -0.1362, -0.2405, -0.0478, 0.2039],
                                        [0.0406, -0.0095, -0.0478, -0.0271, 0.0271, 0.0478, 0.0095, -0.0406],
                                        [0.1728, -0.0406, -0.2039, -0.1155, 0.1155, 0.2039, 0.0406, -0.1728],
                                        [-0.1728, 0.0406, 0.2039, 0.1155, -0.1155, -0.2039, -0.0406, 0.1728],
                                        [-0.0406, 0.0095, 0.0478, 0.0271, -0.0271, -0.0478, -0.0095, 0.0406],
                                        [0.2039, -0.0478, -0.2405, -0.1362, 0.1362, 0.2405, 0.0478, -0.2039],
                                        [-0.1155, 0.0271, 0.1362, 0.0772, -0.0772, -0.1362, -0.0271, 0.1155]])
        self.filter[30] = torch.Tensor([[0.0795, -0.0187, -0.0938, -0.0532, 0.0532, 0.0938, 0.0187, -0.0795],
                                        [-0.1920, 0.0451, 0.2265, 0.1283, -0.1283, -0.2265, -0.0451, 0.1920],
                                        [0.1920, -0.0451, -0.2265, -0.1283, 0.1283, 0.2265, 0.0451, -0.1920],
                                        [-0.0795, 0.0187, 0.0938, 0.0532, -0.0532, -0.0938, -0.0187, 0.0795],
                                        [-0.0795, 0.0187, 0.0938, 0.0532, -0.0532, -0.0938, -0.0187, 0.0795],
                                        [0.1920, -0.0451, -0.2265, -0.1283, 0.1283, 0.2265, 0.0451, -0.1920],
                                        [-0.1920, 0.0451, 0.2265, 0.1283, -0.1283, -0.2265, -0.0451, 0.1920],
                                        [0.0795, -0.0187, -0.0938, -0.0532, 0.0532, 0.0938, 0.0187, -0.0795]])
        self.filter[31] = torch.Tensor([[0.0406, -0.0095, -0.0478, -0.0271, 0.0271, 0.0478, 0.0095, -0.0406],
                                        [-0.1155, 0.0271, 0.1362, 0.0772, -0.0772, -0.1362, -0.0271, 0.1155],
                                        [0.1728, -0.0406, -0.2039, -0.1155, 0.1155, 0.2039, 0.0406, -0.1728],
                                        [-0.2039, 0.0478, 0.2405, 0.1362, -0.1362, -0.2405, -0.0478, 0.2039],
                                        [0.2039, -0.0478, -0.2405, -0.1362, 0.1362, 0.2405, 0.0478, -0.2039],
                                        [-0.1728, 0.0406, 0.2039, 0.1155, -0.1155, -0.2039, -0.0406, 0.1728],
                                        [0.1155, -0.0271, -0.1362, -0.0772, 0.0772, 0.1362, 0.0271, -0.1155],
                                        [-0.0406, 0.0095, 0.0478, 0.0271, -0.0271, -0.0478, -0.0095, 0.0406]])
        self.filter[32] = torch.Tensor([[0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250]])
        self.filter[33] = torch.Tensor([[0.1734, -0.1734, -0.1734, 0.1734, 0.1734, -0.1734, -0.1734, 0.1734],
                                        [0.1470, -0.1470, -0.1470, 0.1470, 0.1470, -0.1470, -0.1470, 0.1470],
                                        [0.0982, -0.0982, -0.0982, 0.0982, 0.0982, -0.0982, -0.0982, 0.0982],
                                        [0.0345, -0.0345, -0.0345, 0.0345, 0.0345, -0.0345, -0.0345, 0.0345],
                                        [-0.0345, 0.0345, 0.0345, -0.0345, -0.0345, 0.0345, 0.0345, -0.0345],
                                        [-0.0982, 0.0982, 0.0982, -0.0982, -0.0982, 0.0982, 0.0982, -0.0982],
                                        [-0.1470, 0.1470, 0.1470, -0.1470, -0.1470, 0.1470, 0.1470, -0.1470],
                                        [-0.1734, 0.1734, 0.1734, -0.1734, -0.1734, 0.1734, 0.1734, -0.1734]])
        self.filter[34] = torch.Tensor([[0.1633, -0.1633, -0.1633, 0.1633, 0.1633, -0.1633, -0.1633, 0.1633],
                                        [0.0676, -0.0676, -0.0676, 0.0676, 0.0676, -0.0676, -0.0676, 0.0676],
                                        [-0.0676, 0.0676, 0.0676, -0.0676, -0.0676, 0.0676, 0.0676, -0.0676],
                                        [-0.1633, 0.1633, 0.1633, -0.1633, -0.1633, 0.1633, 0.1633, -0.1633],
                                        [-0.1633, 0.1633, 0.1633, -0.1633, -0.1633, 0.1633, 0.1633, -0.1633],
                                        [-0.0676, 0.0676, 0.0676, -0.0676, -0.0676, 0.0676, 0.0676, -0.0676],
                                        [0.0676, -0.0676, -0.0676, 0.0676, 0.0676, -0.0676, -0.0676, 0.0676],
                                        [0.1633, -0.1633, -0.1633, 0.1633, 0.1633, -0.1633, -0.1633, 0.1633]])
        self.filter[35] = torch.Tensor([[0.1470, -0.1470, -0.1470, 0.1470, 0.1470, -0.1470, -0.1470, 0.1470],
                                        [-0.0345, 0.0345, 0.0345, -0.0345, -0.0345, 0.0345, 0.0345, -0.0345],
                                        [-0.1734, 0.1734, 0.1734, -0.1734, -0.1734, 0.1734, 0.1734, -0.1734],
                                        [-0.0982, 0.0982, 0.0982, -0.0982, -0.0982, 0.0982, 0.0982, -0.0982],
                                        [0.0982, -0.0982, -0.0982, 0.0982, 0.0982, -0.0982, -0.0982, 0.0982],
                                        [0.1734, -0.1734, -0.1734, 0.1734, 0.1734, -0.1734, -0.1734, 0.1734],
                                        [0.0345, -0.0345, -0.0345, 0.0345, 0.0345, -0.0345, -0.0345, 0.0345],
                                        [-0.1470, 0.1470, 0.1470, -0.1470, -0.1470, 0.1470, 0.1470, -0.1470]])
        self.filter[36] = torch.Tensor([[0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [-0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250],
                                        [-0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250],
                                        [-0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250],
                                        [-0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250],
                                        [0.1250, -0.1250, -0.1250, 0.1250, 0.1250, -0.1250, -0.1250, 0.1250]])
        self.filter[37] = torch.Tensor([[0.0982, -0.0982, -0.0982, 0.0982, 0.0982, -0.0982, -0.0982, 0.0982],
                                        [-0.1734, 0.1734, 0.1734, -0.1734, -0.1734, 0.1734, 0.1734, -0.1734],
                                        [0.0345, -0.0345, -0.0345, 0.0345, 0.0345, -0.0345, -0.0345, 0.0345],
                                        [0.1470, -0.1470, -0.1470, 0.1470, 0.1470, -0.1470, -0.1470, 0.1470],
                                        [-0.1470, 0.1470, 0.1470, -0.1470, -0.1470, 0.1470, 0.1470, -0.1470],
                                        [-0.0345, 0.0345, 0.0345, -0.0345, -0.0345, 0.0345, 0.0345, -0.0345],
                                        [0.1734, -0.1734, -0.1734, 0.1734, 0.1734, -0.1734, -0.1734, 0.1734],
                                        [-0.0982, 0.0982, 0.0982, -0.0982, -0.0982, 0.0982, 0.0982, -0.0982]])
        self.filter[38] = torch.Tensor([[0.0676, -0.0676, -0.0676, 0.0676, 0.0676, -0.0676, -0.0676, 0.0676],
                                        [-0.1633, 0.1633, 0.1633, -0.1633, -0.1633, 0.1633, 0.1633, -0.1633],
                                        [0.1633, -0.1633, -0.1633, 0.1633, 0.1633, -0.1633, -0.1633, 0.1633],
                                        [-0.0676, 0.0676, 0.0676, -0.0676, -0.0676, 0.0676, 0.0676, -0.0676],
                                        [-0.0676, 0.0676, 0.0676, -0.0676, -0.0676, 0.0676, 0.0676, -0.0676],
                                        [0.1633, -0.1633, -0.1633, 0.1633, 0.1633, -0.1633, -0.1633, 0.1633],
                                        [-0.1633, 0.1633, 0.1633, -0.1633, -0.1633, 0.1633, 0.1633, -0.1633],
                                        [0.0676, -0.0676, -0.0676, 0.0676, 0.0676, -0.0676, -0.0676, 0.0676]])
        self.filter[39] = torch.Tensor([[0.0345, -0.0345, -0.0345, 0.0345, 0.0345, -0.0345, -0.0345, 0.0345],
                                        [-0.0982, 0.0982, 0.0982, -0.0982, -0.0982, 0.0982, 0.0982, -0.0982],
                                        [0.1470, -0.1470, -0.1470, 0.1470, 0.1470, -0.1470, -0.1470, 0.1470],
                                        [-0.1734, 0.1734, 0.1734, -0.1734, -0.1734, 0.1734, 0.1734, -0.1734],
                                        [0.1734, -0.1734, -0.1734, 0.1734, 0.1734, -0.1734, -0.1734, 0.1734],
                                        [-0.1470, 0.1470, 0.1470, -0.1470, -0.1470, 0.1470, 0.1470, -0.1470],
                                        [0.0982, -0.0982, -0.0982, 0.0982, 0.0982, -0.0982, -0.0982, 0.0982],
                                        [-0.0345, 0.0345, 0.0345, -0.0345, -0.0345, 0.0345, 0.0345, -0.0345]])
        self.filter[40] = torch.Tensor([[0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982]])
        self.filter[41] = torch.Tensor([[0.1362, -0.2405, 0.0478, 0.2039, -0.2039, -0.0478, 0.2405, -0.1362],
                                        [0.1155, -0.2039, 0.0406, 0.1728, -0.1728, -0.0406, 0.2039, -0.1155],
                                        [0.0772, -0.1362, 0.0271, 0.1155, -0.1155, -0.0271, 0.1362, -0.0772],
                                        [0.0271, -0.0478, 0.0095, 0.0406, -0.0406, -0.0095, 0.0478, -0.0271],
                                        [-0.0271, 0.0478, -0.0095, -0.0406, 0.0406, 0.0095, -0.0478, 0.0271],
                                        [-0.0772, 0.1362, -0.0271, -0.1155, 0.1155, 0.0271, -0.1362, 0.0772],
                                        [-0.1155, 0.2039, -0.0406, -0.1728, 0.1728, 0.0406, -0.2039, 0.1155],
                                        [-0.1362, 0.2405, -0.0478, -0.2039, 0.2039, 0.0478, -0.2405, 0.1362]])
        self.filter[42] = torch.Tensor([[0.1283, -0.2265, 0.0451, 0.1920, -0.1920, -0.0451, 0.2265, -0.1283],
                                        [0.0532, -0.0938, 0.0187, 0.0795, -0.0795, -0.0187, 0.0938, -0.0532],
                                        [-0.0532, 0.0938, -0.0187, -0.0795, 0.0795, 0.0187, -0.0938, 0.0532],
                                        [-0.1283, 0.2265, -0.0451, -0.1920, 0.1920, 0.0451, -0.2265, 0.1283],
                                        [-0.1283, 0.2265, -0.0451, -0.1920, 0.1920, 0.0451, -0.2265, 0.1283],
                                        [-0.0532, 0.0938, -0.0187, -0.0795, 0.0795, 0.0187, -0.0938, 0.0532],
                                        [0.0532, -0.0938, 0.0187, 0.0795, -0.0795, -0.0187, 0.0938, -0.0532],
                                        [0.1283, -0.2265, 0.0451, 0.1920, -0.1920, -0.0451, 0.2265, -0.1283]])
        self.filter[43] = torch.Tensor([[0.1155, -0.2039, 0.0406, 0.1728, -0.1728, -0.0406, 0.2039, -0.1155],
                                        [-0.0271, 0.0478, -0.0095, -0.0406, 0.0406, 0.0095, -0.0478, 0.0271],
                                        [-0.1362, 0.2405, -0.0478, -0.2039, 0.2039, 0.0478, -0.2405, 0.1362],
                                        [-0.0772, 0.1362, -0.0271, -0.1155, 0.1155, 0.0271, -0.1362, 0.0772],
                                        [0.0772, -0.1362, 0.0271, 0.1155, -0.1155, -0.0271, 0.1362, -0.0772],
                                        [0.1362, -0.2405, 0.0478, 0.2039, -0.2039, -0.0478, 0.2405, -0.1362],
                                        [0.0271, -0.0478, 0.0095, 0.0406, -0.0406, -0.0095, 0.0478, -0.0271],
                                        [-0.1155, 0.2039, -0.0406, -0.1728, 0.1728, 0.0406, -0.2039, 0.1155]])
        self.filter[44] = torch.Tensor([[0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [-0.0982, 0.1734, -0.0345, -0.1470, 0.1470, 0.0345, -0.1734, 0.0982],
                                        [-0.0982, 0.1734, -0.0345, -0.1470, 0.1470, 0.0345, -0.1734, 0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982],
                                        [-0.0982, 0.1734, -0.0345, -0.1470, 0.1470, 0.0345, -0.1734, 0.0982],
                                        [-0.0982, 0.1734, -0.0345, -0.1470, 0.1470, 0.0345, -0.1734, 0.0982],
                                        [0.0982, -0.1734, 0.0345, 0.1470, -0.1470, -0.0345, 0.1734, -0.0982]])
        self.filter[45] = torch.Tensor([[0.0772, -0.1362, 0.0271, 0.1155, -0.1155, -0.0271, 0.1362, -0.0772],
                                        [-0.1362, 0.2405, -0.0478, -0.2039, 0.2039, 0.0478, -0.2405, 0.1362],
                                        [0.0271, -0.0478, 0.0095, 0.0406, -0.0406, -0.0095, 0.0478, -0.0271],
                                        [0.1155, -0.2039, 0.0406, 0.1728, -0.1728, -0.0406, 0.2039, -0.1155],
                                        [-0.1155, 0.2039, -0.0406, -0.1728, 0.1728, 0.0406, -0.2039, 0.1155],
                                        [-0.0271, 0.0478, -0.0095, -0.0406, 0.0406, 0.0095, -0.0478, 0.0271],
                                        [0.1362, -0.2405, 0.0478, 0.2039, -0.2039, -0.0478, 0.2405, -0.1362],
                                        [-0.0772, 0.1362, -0.0271, -0.1155, 0.1155, 0.0271, -0.1362, 0.0772]])
        self.filter[46] = torch.Tensor([[0.0532, -0.0938, 0.0187, 0.0795, -0.0795, -0.0187, 0.0938, -0.0532],
                                        [-0.1283, 0.2265, -0.0451, -0.1920, 0.1920, 0.0451, -0.2265, 0.1283],
                                        [0.1283, -0.2265, 0.0451, 0.1920, -0.1920, -0.0451, 0.2265, -0.1283],
                                        [-0.0532, 0.0938, -0.0187, -0.0795, 0.0795, 0.0187, -0.0938, 0.0532],
                                        [-0.0532, 0.0938, -0.0187, -0.0795, 0.0795, 0.0187, -0.0938, 0.0532],
                                        [0.1283, -0.2265, 0.0451, 0.1920, -0.1920, -0.0451, 0.2265, -0.1283],
                                        [-0.1283, 0.2265, -0.0451, -0.1920, 0.1920, 0.0451, -0.2265, 0.1283],
                                        [0.0532, -0.0938, 0.0187, 0.0795, -0.0795, -0.0187, 0.0938, -0.0532]])
        self.filter[47] = torch.Tensor([[0.0271, -0.0478, 0.0095, 0.0406, -0.0406, -0.0095, 0.0478, -0.0271],
                                        [-0.0772, 0.1362, -0.0271, -0.1155, 0.1155, 0.0271, -0.1362, 0.0772],
                                        [0.1155, -0.2039, 0.0406, 0.1728, -0.1728, -0.0406, 0.2039, -0.1155],
                                        [-0.1362, 0.2405, -0.0478, -0.2039, 0.2039, 0.0478, -0.2405, 0.1362],
                                        [0.1362, -0.2405, 0.0478, 0.2039, -0.2039, -0.0478, 0.2405, -0.1362],
                                        [-0.1155, 0.2039, -0.0406, -0.1728, 0.1728, 0.0406, -0.2039, 0.1155],
                                        [0.0772, -0.1362, 0.0271, 0.1155, -0.1155, -0.0271, 0.1362, -0.0772],
                                        [-0.0271, 0.0478, -0.0095, -0.0406, 0.0406, 0.0095, -0.0478, 0.0271]])
        self.filter[48] = torch.Tensor([[0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676]])
        self.filter[49] = torch.Tensor([[0.0938, -0.2265, 0.2265, -0.0938, -0.0938, 0.2265, -0.2265, 0.0938],
                                        [0.0795, -0.1920, 0.1920, -0.0795, -0.0795, 0.1920, -0.1920, 0.0795],
                                        [0.0532, -0.1283, 0.1283, -0.0532, -0.0532, 0.1283, -0.1283, 0.0532],
                                        [0.0187, -0.0451, 0.0451, -0.0187, -0.0187, 0.0451, -0.0451, 0.0187],
                                        [-0.0187, 0.0451, -0.0451, 0.0187, 0.0187, -0.0451, 0.0451, -0.0187],
                                        [-0.0532, 0.1283, -0.1283, 0.0532, 0.0532, -0.1283, 0.1283, -0.0532],
                                        [-0.0795, 0.1920, -0.1920, 0.0795, 0.0795, -0.1920, 0.1920, -0.0795],
                                        [-0.0938, 0.2265, -0.2265, 0.0938, 0.0938, -0.2265, 0.2265, -0.0938]])
        self.filter[50] = torch.Tensor([[0.0884, -0.2134, 0.2134, -0.0884, -0.0884, 0.2134, -0.2134, 0.0884],
                                        [0.0366, -0.0884, 0.0884, -0.0366, -0.0366, 0.0884, -0.0884, 0.0366],
                                        [-0.0366, 0.0884, -0.0884, 0.0366, 0.0366, -0.0884, 0.0884, -0.0366],
                                        [-0.0884, 0.2134, -0.2134, 0.0884, 0.0884, -0.2134, 0.2134, -0.0884],
                                        [-0.0884, 0.2134, -0.2134, 0.0884, 0.0884, -0.2134, 0.2134, -0.0884],
                                        [-0.0366, 0.0884, -0.0884, 0.0366, 0.0366, -0.0884, 0.0884, -0.0366],
                                        [0.0366, -0.0884, 0.0884, -0.0366, -0.0366, 0.0884, -0.0884, 0.0366],
                                        [0.0884, -0.2134, 0.2134, -0.0884, -0.0884, 0.2134, -0.2134, 0.0884]])
        self.filter[51] = torch.Tensor([[0.0795, -0.1920, 0.1920, -0.0795, -0.0795, 0.1920, -0.1920, 0.0795],
                                        [-0.0187, 0.0451, -0.0451, 0.0187, 0.0187, -0.0451, 0.0451, -0.0187],
                                        [-0.0938, 0.2265, -0.2265, 0.0938, 0.0938, -0.2265, 0.2265, -0.0938],
                                        [-0.0532, 0.1283, -0.1283, 0.0532, 0.0532, -0.1283, 0.1283, -0.0532],
                                        [0.0532, -0.1283, 0.1283, -0.0532, -0.0532, 0.1283, -0.1283, 0.0532],
                                        [0.0938, -0.2265, 0.2265, -0.0938, -0.0938, 0.2265, -0.2265, 0.0938],
                                        [0.0187, -0.0451, 0.0451, -0.0187, -0.0187, 0.0451, -0.0451, 0.0187],
                                        [-0.0795, 0.1920, -0.1920, 0.0795, 0.0795, -0.1920, 0.1920, -0.0795]])
        self.filter[52] = torch.Tensor([[0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [-0.0676, 0.1633, -0.1633, 0.0676, 0.0676, -0.1633, 0.1633, -0.0676],
                                        [-0.0676, 0.1633, -0.1633, 0.0676, 0.0676, -0.1633, 0.1633, -0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676],
                                        [-0.0676, 0.1633, -0.1633, 0.0676, 0.0676, -0.1633, 0.1633, -0.0676],
                                        [-0.0676, 0.1633, -0.1633, 0.0676, 0.0676, -0.1633, 0.1633, -0.0676],
                                        [0.0676, -0.1633, 0.1633, -0.0676, -0.0676, 0.1633, -0.1633, 0.0676]])
        self.filter[53] = torch.Tensor([[0.0532, -0.1283, 0.1283, -0.0532, -0.0532, 0.1283, -0.1283, 0.0532],
                                        [-0.0938, 0.2265, -0.2265, 0.0938, 0.0938, -0.2265, 0.2265, -0.0938],
                                        [0.0187, -0.0451, 0.0451, -0.0187, -0.0187, 0.0451, -0.0451, 0.0187],
                                        [0.0795, -0.1920, 0.1920, -0.0795, -0.0795, 0.1920, -0.1920, 0.0795],
                                        [-0.0795, 0.1920, -0.1920, 0.0795, 0.0795, -0.1920, 0.1920, -0.0795],
                                        [-0.0187, 0.0451, -0.0451, 0.0187, 0.0187, -0.0451, 0.0451, -0.0187],
                                        [0.0938, -0.2265, 0.2265, -0.0938, -0.0938, 0.2265, -0.2265, 0.0938],
                                        [-0.0532, 0.1283, -0.1283, 0.0532, 0.0532, -0.1283, 0.1283, -0.0532]])
        self.filter[54] = torch.Tensor([[0.0366, -0.0884, 0.0884, -0.0366, -0.0366, 0.0884, -0.0884, 0.0366],
                                        [-0.0884, 0.2134, -0.2134, 0.0884, 0.0884, -0.2134, 0.2134, -0.0884],
                                        [0.0884, -0.2134, 0.2134, -0.0884, -0.0884, 0.2134, -0.2134, 0.0884],
                                        [-0.0366, 0.0884, -0.0884, 0.0366, 0.0366, -0.0884, 0.0884, -0.0366],
                                        [-0.0366, 0.0884, -0.0884, 0.0366, 0.0366, -0.0884, 0.0884, -0.0366],
                                        [0.0884, -0.2134, 0.2134, -0.0884, -0.0884, 0.2134, -0.2134, 0.0884],
                                        [-0.0884, 0.2134, -0.2134, 0.0884, 0.0884, -0.2134, 0.2134, -0.0884],
                                        [0.0366, -0.0884, 0.0884, -0.0366, -0.0366, 0.0884, -0.0884, 0.0366]])
        self.filter[55] = torch.Tensor([[0.0187, -0.0451, 0.0451, -0.0187, -0.0187, 0.0451, -0.0451, 0.0187],
                                        [-0.0532, 0.1283, -0.1283, 0.0532, 0.0532, -0.1283, 0.1283, -0.0532],
                                        [0.0795, -0.1920, 0.1920, -0.0795, -0.0795, 0.1920, -0.1920, 0.0795],
                                        [-0.0938, 0.2265, -0.2265, 0.0938, 0.0938, -0.2265, 0.2265, -0.0938],
                                        [0.0938, -0.2265, 0.2265, -0.0938, -0.0938, 0.2265, -0.2265, 0.0938],
                                        [-0.0795, 0.1920, -0.1920, 0.0795, 0.0795, -0.1920, 0.1920, -0.0795],
                                        [0.0532, -0.1283, 0.1283, -0.0532, -0.0532, 0.1283, -0.1283, 0.0532],
                                        [-0.0187, 0.0451, -0.0451, 0.0187, 0.0187, -0.0451, 0.0451, -0.0187]])
        self.filter[56] = torch.Tensor([[0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345]])
        self.filter[57] = torch.Tensor([[0.0478, -0.1362, 0.2039, -0.2405, 0.2405, -0.2039, 0.1362, -0.0478],
                                        [0.0406, -0.1155, 0.1728, -0.2039, 0.2039, -0.1728, 0.1155, -0.0406],
                                        [0.0271, -0.0772, 0.1155, -0.1362, 0.1362, -0.1155, 0.0772, -0.0271],
                                        [0.0095, -0.0271, 0.0406, -0.0478, 0.0478, -0.0406, 0.0271, -0.0095],
                                        [-0.0095, 0.0271, -0.0406, 0.0478, -0.0478, 0.0406, -0.0271, 0.0095],
                                        [-0.0271, 0.0772, -0.1155, 0.1362, -0.1362, 0.1155, -0.0772, 0.0271],
                                        [-0.0406, 0.1155, -0.1728, 0.2039, -0.2039, 0.1728, -0.1155, 0.0406],
                                        [-0.0478, 0.1362, -0.2039, 0.2405, -0.2405, 0.2039, -0.1362, 0.0478]])
        self.filter[58] = torch.Tensor([[0.0451, -0.1283, 0.1920, -0.2265, 0.2265, -0.1920, 0.1283, -0.0451],
                                        [0.0187, -0.0532, 0.0795, -0.0938, 0.0938, -0.0795, 0.0532, -0.0187],
                                        [-0.0187, 0.0532, -0.0795, 0.0938, -0.0938, 0.0795, -0.0532, 0.0187],
                                        [-0.0451, 0.1283, -0.1920, 0.2265, -0.2265, 0.1920, -0.1283, 0.0451],
                                        [-0.0451, 0.1283, -0.1920, 0.2265, -0.2265, 0.1920, -0.1283, 0.0451],
                                        [-0.0187, 0.0532, -0.0795, 0.0938, -0.0938, 0.0795, -0.0532, 0.0187],
                                        [0.0187, -0.0532, 0.0795, -0.0938, 0.0938, -0.0795, 0.0532, -0.0187],
                                        [0.0451, -0.1283, 0.1920, -0.2265, 0.2265, -0.1920, 0.1283, -0.0451]])
        self.filter[59] = torch.Tensor([[0.0406, -0.1155, 0.1728, -0.2039, 0.2039, -0.1728, 0.1155, -0.0406],
                                        [-0.0095, 0.0271, -0.0406, 0.0478, -0.0478, 0.0406, -0.0271, 0.0095],
                                        [-0.0478, 0.1362, -0.2039, 0.2405, -0.2405, 0.2039, -0.1362, 0.0478],
                                        [-0.0271, 0.0772, -0.1155, 0.1362, -0.1362, 0.1155, -0.0772, 0.0271],
                                        [0.0271, -0.0772, 0.1155, -0.1362, 0.1362, -0.1155, 0.0772, -0.0271],
                                        [0.0478, -0.1362, 0.2039, -0.2405, 0.2405, -0.2039, 0.1362, -0.0478],
                                        [0.0095, -0.0271, 0.0406, -0.0478, 0.0478, -0.0406, 0.0271, -0.0095],
                                        [-0.0406, 0.1155, -0.1728, 0.2039, -0.2039, 0.1728, -0.1155, 0.0406]])
        self.filter[60] = torch.Tensor([[0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [-0.0345, 0.0982, -0.1470, 0.1734, -0.1734, 0.1470, -0.0982, 0.0345],
                                        [-0.0345, 0.0982, -0.1470, 0.1734, -0.1734, 0.1470, -0.0982, 0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345],
                                        [-0.0345, 0.0982, -0.1470, 0.1734, -0.1734, 0.1470, -0.0982, 0.0345],
                                        [-0.0345, 0.0982, -0.1470, 0.1734, -0.1734, 0.1470, -0.0982, 0.0345],
                                        [0.0345, -0.0982, 0.1470, -0.1734, 0.1734, -0.1470, 0.0982, -0.0345]])
        self.filter[61] = torch.Tensor([[0.0271, -0.0772, 0.1155, -0.1362, 0.1362, -0.1155, 0.0772, -0.0271],
                                        [-0.0478, 0.1362, -0.2039, 0.2405, -0.2405, 0.2039, -0.1362, 0.0478],
                                        [0.0095, -0.0271, 0.0406, -0.0478, 0.0478, -0.0406, 0.0271, -0.0095],
                                        [0.0406, -0.1155, 0.1728, -0.2039, 0.2039, -0.1728, 0.1155, -0.0406],
                                        [-0.0406, 0.1155, -0.1728, 0.2039, -0.2039, 0.1728, -0.1155, 0.0406],
                                        [-0.0095, 0.0271, -0.0406, 0.0478, -0.0478, 0.0406, -0.0271, 0.0095],
                                        [0.0478, -0.1362, 0.2039, -0.2405, 0.2405, -0.2039, 0.1362, -0.0478],
                                        [-0.0271, 0.0772, -0.1155, 0.1362, -0.1362, 0.1155, -0.0772, 0.0271]])
        self.filter[62] = torch.Tensor([[0.0187, -0.0532, 0.0795, -0.0938, 0.0938, -0.0795, 0.0532, -0.0187],
                                        [-0.0451, 0.1283, -0.1920, 0.2265, -0.2265, 0.1920, -0.1283, 0.0451],
                                        [0.0451, -0.1283, 0.1920, -0.2265, 0.2265, -0.1920, 0.1283, -0.0451],
                                        [-0.0187, 0.0532, -0.0795, 0.0938, -0.0938, 0.0795, -0.0532, 0.0187],
                                        [-0.0187, 0.0532, -0.0795, 0.0938, -0.0938, 0.0795, -0.0532, 0.0187],
                                        [0.0451, -0.1283, 0.1920, -0.2265, 0.2265, -0.1920, 0.1283, -0.0451],
                                        [-0.0451, 0.1283, -0.1920, 0.2265, -0.2265, 0.1920, -0.1283, 0.0451],
                                        [0.0187, -0.0532, 0.0795, -0.0938, 0.0938, -0.0795, 0.0532, -0.0187]])
        self.filter[63] = torch.Tensor([[0.0095, -0.0271, 0.0406, -0.0478, 0.0478, -0.0406, 0.0271, -0.0095],
                                        [-0.0271, 0.0772, -0.1155, 0.1362, -0.1362, 0.1155, -0.0772, 0.0271],
                                        [0.0406, -0.1155, 0.1728, -0.2039, 0.2039, -0.1728, 0.1155, -0.0406],
                                        [-0.0478, 0.1362, -0.2039, 0.2405, -0.2405, 0.2039, -0.1362, 0.0478],
                                        [0.0478, -0.1362, 0.2039, -0.2405, 0.2405, -0.2039, 0.1362, -0.0478],
                                        [-0.0406, 0.1155, -0.1728, 0.2039, -0.2039, 0.1728, -0.1155, 0.0406],
                                        [0.0271, -0.0772, 0.1155, -0.1362, 0.1362, -0.1155, 0.0772, -0.0271],
                                        [-0.0095, 0.0271, -0.0406, 0.0478, -0.0478, 0.0406, -0.0271, 0.0095]])

        # self.filter = self.filter.view(8, 8, 8, 8)

    def get_filter(self, idx):
        return self.filter[idx]


class DCT7x7(nn.Module):
    def __init__(self):
        super(DCT7x7, self).__init__()
        self.filter = torch.zeros(49, 7, 7).cuda()
        self.freq_num = 49
        self.freq_range = 7

        self.filter[0] = torch.Tensor([[0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429],
                                       [0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429],
                                       [0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429],
                                       [0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429],
                                       [0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429],
                                       [0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429],
                                       [0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429, 0.1429]])
        self.filter[1] = torch.Tensor([[1.9697e-01, 1.9697e-01, 1.9697e-01, 1.9697e-01, 1.9697e-01,
                                        1.9697e-01, 1.9697e-01],
                                       [1.5795e-01, 1.5795e-01, 1.5795e-01, 1.5795e-01, 1.5795e-01,
                                        1.5795e-01, 1.5795e-01],
                                       [8.7658e-02, 8.7658e-02, 8.7658e-02, 8.7658e-02, 8.7658e-02,
                                        8.7658e-02, 8.7658e-02],
                                       [1.2371e-17, 1.2371e-17, 1.2371e-17, 1.2371e-17, 1.2371e-17,
                                        1.2371e-17, 1.2371e-17],
                                       [-8.7658e-02, -8.7658e-02, -8.7658e-02, -8.7658e-02, -8.7658e-02,
                                        -8.7658e-02, -8.7658e-02],
                                       [-1.5795e-01, -1.5795e-01, -1.5795e-01, -1.5795e-01, -1.5795e-01,
                                        -1.5795e-01, -1.5795e-01],
                                       [-1.9697e-01, -1.9697e-01, -1.9697e-01, -1.9697e-01, -1.9697e-01,
                                        -1.9697e-01, -1.9697e-01]])
        self.filter[2] = torch.Tensor([[0.1820, 0.1820, 0.1820, 0.1820, 0.1820, 0.1820, 0.1820],
                                       [0.0450, 0.0450, 0.0450, 0.0450, 0.0450, 0.0450, 0.0450],
                                       [-0.1260, -0.1260, -0.1260, -0.1260, -0.1260, -0.1260, -0.1260],
                                       [-0.2020, -0.2020, -0.2020, -0.2020, -0.2020, -0.2020, -0.2020],
                                       [-0.1260, -0.1260, -0.1260, -0.1260, -0.1260, -0.1260, -0.1260],
                                       [0.0450, 0.0450, 0.0450, 0.0450, 0.0450, 0.0450, 0.0450],
                                       [0.1820, 0.1820, 0.1820, 0.1820, 0.1820, 0.1820, 0.1820]])
        self.filter[3] = torch.Tensor([[1.5795e-01, 1.5795e-01, 1.5795e-01, 1.5795e-01, 1.5795e-01,
                                        1.5795e-01, 1.5795e-01],
                                       [-8.7658e-02, -8.7658e-02, -8.7658e-02, -8.7658e-02, -8.7658e-02,
                                        -8.7658e-02, -8.7658e-02],
                                       [-1.9697e-01, -1.9697e-01, -1.9697e-01, -1.9697e-01, -1.9697e-01,
                                        -1.9697e-01, -1.9697e-01],
                                       [-3.7112e-17, -3.7112e-17, -3.7112e-17, -3.7112e-17, -3.7112e-17,
                                        -3.7112e-17, -3.7112e-17],
                                       [1.9697e-01, 1.9697e-01, 1.9697e-01, 1.9697e-01, 1.9697e-01,
                                        1.9697e-01, 1.9697e-01],
                                       [8.7658e-02, 8.7658e-02, 8.7658e-02, 8.7658e-02, 8.7658e-02,
                                        8.7658e-02, 8.7658e-02],
                                       [-1.5795e-01, -1.5795e-01, -1.5795e-01, -1.5795e-01, -1.5795e-01,
                                        -1.5795e-01, -1.5795e-01]])
        self.filter[4] = torch.Tensor([[0.1260, 0.1260, 0.1260, 0.1260, 0.1260, 0.1260, 0.1260],
                                       [-0.1820, -0.1820, -0.1820, -0.1820, -0.1820, -0.1820, -0.1820],
                                       [-0.0450, -0.0450, -0.0450, -0.0450, -0.0450, -0.0450, -0.0450],
                                       [0.2020, 0.2020, 0.2020, 0.2020, 0.2020, 0.2020, 0.2020],
                                       [-0.0450, -0.0450, -0.0450, -0.0450, -0.0450, -0.0450, -0.0450],
                                       [-0.1820, -0.1820, -0.1820, -0.1820, -0.1820, -0.1820, -0.1820],
                                       [0.1260, 0.1260, 0.1260, 0.1260, 0.1260, 0.1260, 0.1260]])
        self.filter[5] = torch.Tensor([[8.7658e-02, 8.7658e-02, 8.7658e-02, 8.7658e-02, 8.7658e-02,
                                        8.7658e-02, 8.7658e-02],
                                       [-1.9697e-01, -1.9697e-01, -1.9697e-01, -1.9697e-01, -1.9697e-01,
                                        -1.9697e-01, -1.9697e-01],
                                       [1.5795e-01, 1.5795e-01, 1.5795e-01, 1.5795e-01, 1.5795e-01,
                                        1.5795e-01, 1.5795e-01],
                                       [6.1854e-17, 6.1854e-17, 6.1854e-17, 6.1854e-17, 6.1854e-17,
                                        6.1854e-17, 6.1854e-17],
                                       [-1.5795e-01, -1.5795e-01, -1.5795e-01, -1.5795e-01, -1.5795e-01,
                                        -1.5795e-01, -1.5795e-01],
                                       [1.9697e-01, 1.9697e-01, 1.9697e-01, 1.9697e-01, 1.9697e-01,
                                        1.9697e-01, 1.9697e-01],
                                       [-8.7658e-02, -8.7658e-02, -8.7658e-02, -8.7658e-02, -8.7658e-02,
                                        -8.7658e-02, -8.7658e-02]])
        self.filter[6] = torch.Tensor([[0.0450, 0.0450, 0.0450, 0.0450, 0.0450, 0.0450, 0.0450],
                                       [-0.1260, -0.1260, -0.1260, -0.1260, -0.1260, -0.1260, -0.1260],
                                       [0.1820, 0.1820, 0.1820, 0.1820, 0.1820, 0.1820, 0.1820],
                                       [-0.2020, -0.2020, -0.2020, -0.2020, -0.2020, -0.2020, -0.2020],
                                       [0.1820, 0.1820, 0.1820, 0.1820, 0.1820, 0.1820, 0.1820],
                                       [-0.1260, -0.1260, -0.1260, -0.1260, -0.1260, -0.1260, -0.1260],
                                       [0.0450, 0.0450, 0.0450, 0.0450, 0.0450, 0.0450, 0.0450]])
        self.filter[7] = torch.Tensor([[1.9697e-01, 1.5795e-01, 8.7658e-02, 1.2371e-17, -8.7658e-02,
                                        -1.5795e-01, -1.9697e-01],
                                       [1.9697e-01, 1.5795e-01, 8.7658e-02, 1.2371e-17, -8.7658e-02,
                                        -1.5795e-01, -1.9697e-01],
                                       [1.9697e-01, 1.5795e-01, 8.7658e-02, 1.2371e-17, -8.7658e-02,
                                        -1.5795e-01, -1.9697e-01],
                                       [1.9697e-01, 1.5795e-01, 8.7658e-02, 1.2371e-17, -8.7658e-02,
                                        -1.5795e-01, -1.9697e-01],
                                       [1.9697e-01, 1.5795e-01, 8.7658e-02, 1.2371e-17, -8.7658e-02,
                                        -1.5795e-01, -1.9697e-01],
                                       [1.9697e-01, 1.5795e-01, 8.7658e-02, 1.2371e-17, -8.7658e-02,
                                        -1.5795e-01, -1.9697e-01],
                                       [1.9697e-01, 1.5795e-01, 8.7658e-02, 1.2371e-17, -8.7658e-02,
                                        -1.5795e-01, -1.9697e-01]])
        self.filter[8] = torch.Tensor([[2.7157e-01, 2.1778e-01, 1.2086e-01, 1.7056e-17, -1.2086e-01,
                                        -2.1778e-01, -2.7157e-01],
                                       [2.1778e-01, 1.7465e-01, 9.6921e-02, 1.3678e-17, -9.6921e-02,
                                        -1.7465e-01, -2.1778e-01],
                                       [1.2086e-01, 9.6921e-02, 5.3787e-02, 7.5908e-18, -5.3787e-02,
                                        -9.6921e-02, -1.2086e-01],
                                       [1.7056e-17, 1.3678e-17, 7.5908e-18, 1.0713e-33, -7.5908e-18,
                                        -1.3678e-17, -1.7056e-17],
                                       [-1.2086e-01, -9.6921e-02, -5.3787e-02, -7.5908e-18, 5.3787e-02,
                                        9.6921e-02, 1.2086e-01],
                                       [-2.1778e-01, -1.7465e-01, -9.6921e-02, -1.3678e-17, 9.6921e-02,
                                        1.7465e-01, 2.1778e-01],
                                       [-2.7157e-01, -2.1778e-01, -1.2086e-01, -1.7056e-17, 1.2086e-01,
                                        2.1778e-01, 2.7157e-01]])
        self.filter[9] = torch.Tensor([[2.5097e-01, 2.0126e-01, 1.1169e-01, 1.5762e-17, -1.1169e-01,
                                        -2.0126e-01, -2.5097e-01],
                                       [6.1983e-02, 4.9707e-02, 2.7585e-02, 3.8930e-18, -2.7585e-02,
                                        -4.9707e-02, -6.1983e-02],
                                       [-1.7367e-01, -1.3928e-01, -7.7292e-02, -1.0908e-17, 7.7292e-02,
                                        1.3928e-01, 1.7367e-01],
                                       [-2.7855e-01, -2.2338e-01, -1.2397e-01, -1.7495e-17, 1.2397e-01,
                                        2.2338e-01, 2.7855e-01],
                                       [-1.7367e-01, -1.3928e-01, -7.7292e-02, -1.0908e-17, 7.7292e-02,
                                        1.3928e-01, 1.7367e-01],
                                       [6.1983e-02, 4.9707e-02, 2.7585e-02, 3.8930e-18, -2.7585e-02,
                                        -4.9707e-02, -6.1983e-02],
                                       [2.5097e-01, 2.0126e-01, 1.1169e-01, 1.5762e-17, -1.1169e-01,
                                        -2.0126e-01, -2.5097e-01]])
        self.filter[10] = torch.Tensor([[2.1778e-01, 1.7465e-01, 9.6921e-02, 1.3678e-17, -9.6921e-02,
                                         -1.7465e-01, -2.1778e-01],
                                        [-1.2086e-01, -9.6921e-02, -5.3787e-02, -7.5908e-18, 5.3787e-02,
                                         9.6921e-02, 1.2086e-01],
                                        [-2.7157e-01, -2.1778e-01, -1.2086e-01, -1.7056e-17, 1.2086e-01,
                                         2.1778e-01, 2.7157e-01],
                                        [-5.1169e-17, -4.1034e-17, -2.2772e-17, -3.2138e-33, 2.2772e-17,
                                         4.1034e-17, 5.1169e-17],
                                        [2.7157e-01, 2.1778e-01, 1.2086e-01, 1.7056e-17, -1.2086e-01,
                                         -2.1778e-01, -2.7157e-01],
                                        [1.2086e-01, 9.6921e-02, 5.3787e-02, 7.5908e-18, -5.3787e-02,
                                         -9.6921e-02, -1.2086e-01],
                                        [-2.1778e-01, -1.7465e-01, -9.6921e-02, -1.3678e-17, 9.6921e-02,
                                         1.7465e-01, 2.1778e-01]])
        self.filter[11] = torch.Tensor([[1.7367e-01, 1.3928e-01, 7.7292e-02, 1.0908e-17, -7.7292e-02,
                                         -1.3928e-01, -1.7367e-01],
                                        [-2.5097e-01, -2.0126e-01, -1.1169e-01, -1.5762e-17, 1.1169e-01,
                                         2.0126e-01, 2.5097e-01],
                                        [-6.1983e-02, -4.9707e-02, -2.7585e-02, -3.8930e-18, 2.7585e-02,
                                         4.9707e-02, 6.1983e-02],
                                        [2.7855e-01, 2.2338e-01, 1.2397e-01, 1.7495e-17, -1.2397e-01,
                                         -2.2338e-01, -2.7855e-01],
                                        [-6.1983e-02, -4.9707e-02, -2.7585e-02, -3.8930e-18, 2.7585e-02,
                                         4.9707e-02, 6.1983e-02],
                                        [-2.5097e-01, -2.0126e-01, -1.1169e-01, -1.5762e-17, 1.1169e-01,
                                         2.0126e-01, 2.5097e-01],
                                        [1.7367e-01, 1.3928e-01, 7.7292e-02, 1.0908e-17, -7.7292e-02,
                                         -1.3928e-01, -1.7367e-01]])
        self.filter[12] = torch.Tensor([[1.2086e-01, 9.6921e-02, 5.3787e-02, 7.5908e-18, -5.3787e-02,
                                         -9.6921e-02, -1.2086e-01],
                                        [-2.7157e-01, -2.1778e-01, -1.2086e-01, -1.7056e-17, 1.2086e-01,
                                         2.1778e-01, 2.7157e-01],
                                        [2.1778e-01, 1.7465e-01, 9.6921e-02, 1.3678e-17, -9.6921e-02,
                                         -1.7465e-01, -2.1778e-01],
                                        [8.5282e-17, 6.8391e-17, 3.7954e-17, 5.3563e-33, -3.7954e-17,
                                         -6.8391e-17, -8.5282e-17],
                                        [-2.1778e-01, -1.7465e-01, -9.6921e-02, -1.3678e-17, 9.6921e-02,
                                         1.7465e-01, 2.1778e-01],
                                        [2.7157e-01, 2.1778e-01, 1.2086e-01, 1.7056e-17, -1.2086e-01,
                                         -2.1778e-01, -2.7157e-01],
                                        [-1.2086e-01, -9.6921e-02, -5.3787e-02, -7.5908e-18, 5.3787e-02,
                                         9.6921e-02, 1.2086e-01]])
        self.filter[13] = torch.Tensor([[6.1983e-02, 4.9707e-02, 2.7585e-02, 3.8930e-18, -2.7585e-02,
                                         -4.9707e-02, -6.1983e-02],
                                        [-1.7367e-01, -1.3928e-01, -7.7292e-02, -1.0908e-17, 7.7292e-02,
                                         1.3928e-01, 1.7367e-01],
                                        [2.5097e-01, 2.0126e-01, 1.1169e-01, 1.5762e-17, -1.1169e-01,
                                         -2.0126e-01, -2.5097e-01],
                                        [-2.7855e-01, -2.2338e-01, -1.2397e-01, -1.7495e-17, 1.2397e-01,
                                         2.2338e-01, 2.7855e-01],
                                        [2.5097e-01, 2.0126e-01, 1.1169e-01, 1.5762e-17, -1.1169e-01,
                                         -2.0126e-01, -2.5097e-01],
                                        [-1.7367e-01, -1.3928e-01, -7.7292e-02, -1.0908e-17, 7.7292e-02,
                                         1.3928e-01, 1.7367e-01],
                                        [6.1983e-02, 4.9707e-02, 2.7585e-02, 3.8930e-18, -2.7585e-02,
                                         -4.9707e-02, -6.1983e-02]])
        self.filter[14] = torch.Tensor([[0.1820, 0.0450, -0.1260, -0.2020, -0.1260, 0.0450, 0.1820],
                                        [0.1820, 0.0450, -0.1260, -0.2020, -0.1260, 0.0450, 0.1820],
                                        [0.1820, 0.0450, -0.1260, -0.2020, -0.1260, 0.0450, 0.1820],
                                        [0.1820, 0.0450, -0.1260, -0.2020, -0.1260, 0.0450, 0.1820],
                                        [0.1820, 0.0450, -0.1260, -0.2020, -0.1260, 0.0450, 0.1820],
                                        [0.1820, 0.0450, -0.1260, -0.2020, -0.1260, 0.0450, 0.1820],
                                        [0.1820, 0.0450, -0.1260, -0.2020, -0.1260, 0.0450, 0.1820]])
        self.filter[15] = torch.Tensor([[2.5097e-01, 6.1983e-02, -1.7367e-01, -2.7855e-01, -1.7367e-01,
                                         6.1983e-02, 2.5097e-01],
                                        [2.0126e-01, 4.9707e-02, -1.3928e-01, -2.2338e-01, -1.3928e-01,
                                         4.9707e-02, 2.0126e-01],
                                        [1.1169e-01, 2.7585e-02, -7.7292e-02, -1.2397e-01, -7.7292e-02,
                                         2.7585e-02, 1.1169e-01],
                                        [1.5762e-17, 3.8930e-18, -1.0908e-17, -1.7495e-17, -1.0908e-17,
                                         3.8930e-18, 1.5762e-17],
                                        [-1.1169e-01, -2.7585e-02, 7.7292e-02, 1.2397e-01, 7.7292e-02,
                                         -2.7585e-02, -1.1169e-01],
                                        [-2.0126e-01, -4.9707e-02, 1.3928e-01, 2.2338e-01, 1.3928e-01,
                                         -4.9707e-02, -2.0126e-01],
                                        [-2.5097e-01, -6.1983e-02, 1.7367e-01, 2.7855e-01, 1.7367e-01,
                                         -6.1983e-02, -2.5097e-01]])
        self.filter[16] = torch.Tensor([[0.2319, 0.0573, -0.1605, -0.2574, -0.1605, 0.0573, 0.2319],
                                        [0.0573, 0.0141, -0.0396, -0.0636, -0.0396, 0.0141, 0.0573],
                                        [-0.1605, -0.0396, 0.1111, 0.1781, 0.1111, -0.0396, -0.1605],
                                        [-0.2574, -0.0636, 0.1781, 0.2857, 0.1781, -0.0636, -0.2574],
                                        [-0.1605, -0.0396, 0.1111, 0.1781, 0.1111, -0.0396, -0.1605],
                                        [0.0573, 0.0141, -0.0396, -0.0636, -0.0396, 0.0141, 0.0573],
                                        [0.2319, 0.0573, -0.1605, -0.2574, -0.1605, 0.0573, 0.2319]])
        self.filter[17] = torch.Tensor([[2.0126e-01, 4.9707e-02, -1.3928e-01, -2.2338e-01, -1.3928e-01,
                                         4.9707e-02, 2.0126e-01],
                                        [-1.1169e-01, -2.7585e-02, 7.7292e-02, 1.2397e-01, 7.7292e-02,
                                         -2.7585e-02, -1.1169e-01],
                                        [-2.5097e-01, -6.1983e-02, 1.7367e-01, 2.7855e-01, 1.7367e-01,
                                         -6.1983e-02, -2.5097e-01],
                                        [-4.7287e-17, -1.1679e-17, 3.2724e-17, 5.2485e-17, 3.2724e-17,
                                         -1.1679e-17, -4.7287e-17],
                                        [2.5097e-01, 6.1983e-02, -1.7367e-01, -2.7855e-01, -1.7367e-01,
                                         6.1983e-02, 2.5097e-01],
                                        [1.1169e-01, 2.7585e-02, -7.7292e-02, -1.2397e-01, -7.7292e-02,
                                         2.7585e-02, 1.1169e-01],
                                        [-2.0126e-01, -4.9707e-02, 1.3928e-01, 2.2338e-01, 1.3928e-01,
                                         -4.9707e-02, -2.0126e-01]])
        self.filter[18] = torch.Tensor([[0.1605, 0.0396, -0.1111, -0.1781, -0.1111, 0.0396, 0.1605],
                                        [-0.2319, -0.0573, 0.1605, 0.2574, 0.1605, -0.0573, -0.2319],
                                        [-0.0573, -0.0141, 0.0396, 0.0636, 0.0396, -0.0141, -0.0573],
                                        [0.2574, 0.0636, -0.1781, -0.2857, -0.1781, 0.0636, 0.2574],
                                        [-0.0573, -0.0141, 0.0396, 0.0636, 0.0396, -0.0141, -0.0573],
                                        [-0.2319, -0.0573, 0.1605, 0.2574, 0.1605, -0.0573, -0.2319],
                                        [0.1605, 0.0396, -0.1111, -0.1781, -0.1111, 0.0396, 0.1605]])
        self.filter[19] = torch.Tensor([[1.1169e-01, 2.7585e-02, -7.7292e-02, -1.2397e-01, -7.7292e-02,
                                         2.7585e-02, 1.1169e-01],
                                        [-2.5097e-01, -6.1983e-02, 1.7367e-01, 2.7855e-01, 1.7367e-01,
                                         -6.1983e-02, -2.5097e-01],
                                        [2.0126e-01, 4.9707e-02, -1.3928e-01, -2.2338e-01, -1.3928e-01,
                                         4.9707e-02, 2.0126e-01],
                                        [7.8812e-17, 1.9465e-17, -5.4540e-17, -8.7475e-17, -5.4540e-17,
                                         1.9465e-17, 7.8812e-17],
                                        [-2.0126e-01, -4.9707e-02, 1.3928e-01, 2.2338e-01, 1.3928e-01,
                                         -4.9707e-02, -2.0126e-01],
                                        [2.5097e-01, 6.1983e-02, -1.7367e-01, -2.7855e-01, -1.7367e-01,
                                         6.1983e-02, 2.5097e-01],
                                        [-1.1169e-01, -2.7585e-02, 7.7292e-02, 1.2397e-01, 7.7292e-02,
                                         -2.7585e-02, -1.1169e-01]])
        self.filter[20] = torch.Tensor([[0.0573, 0.0141, -0.0396, -0.0636, -0.0396, 0.0141, 0.0573],
                                        [-0.1605, -0.0396, 0.1111, 0.1781, 0.1111, -0.0396, -0.1605],
                                        [0.2319, 0.0573, -0.1605, -0.2574, -0.1605, 0.0573, 0.2319],
                                        [-0.2574, -0.0636, 0.1781, 0.2857, 0.1781, -0.0636, -0.2574],
                                        [0.2319, 0.0573, -0.1605, -0.2574, -0.1605, 0.0573, 0.2319],
                                        [-0.1605, -0.0396, 0.1111, 0.1781, 0.1111, -0.0396, -0.1605],
                                        [0.0573, 0.0141, -0.0396, -0.0636, -0.0396, 0.0141, 0.0573]])
        self.filter[21] = torch.Tensor([[1.5795e-01, -8.7658e-02, -1.9697e-01, -3.7112e-17, 1.9697e-01,
                                         8.7658e-02, -1.5795e-01],
                                        [1.5795e-01, -8.7658e-02, -1.9697e-01, -3.7112e-17, 1.9697e-01,
                                         8.7658e-02, -1.5795e-01],
                                        [1.5795e-01, -8.7658e-02, -1.9697e-01, -3.7112e-17, 1.9697e-01,
                                         8.7658e-02, -1.5795e-01],
                                        [1.5795e-01, -8.7658e-02, -1.9697e-01, -3.7112e-17, 1.9697e-01,
                                         8.7658e-02, -1.5795e-01],
                                        [1.5795e-01, -8.7658e-02, -1.9697e-01, -3.7112e-17, 1.9697e-01,
                                         8.7658e-02, -1.5795e-01],
                                        [1.5795e-01, -8.7658e-02, -1.9697e-01, -3.7112e-17, 1.9697e-01,
                                         8.7658e-02, -1.5795e-01],
                                        [1.5795e-01, -8.7658e-02, -1.9697e-01, -3.7112e-17, 1.9697e-01,
                                         8.7658e-02, -1.5795e-01]])
        self.filter[22] = torch.Tensor([[2.1778e-01, -1.2086e-01, -2.7157e-01, -5.1169e-17, 2.7157e-01,
                                         1.2086e-01, -2.1778e-01],
                                        [1.7465e-01, -9.6921e-02, -2.1778e-01, -4.1034e-17, 2.1778e-01,
                                         9.6921e-02, -1.7465e-01],
                                        [9.6921e-02, -5.3787e-02, -1.2086e-01, -2.2772e-17, 1.2086e-01,
                                         5.3787e-02, -9.6921e-02],
                                        [1.3678e-17, -7.5908e-18, -1.7056e-17, -3.2138e-33, 1.7056e-17,
                                         7.5908e-18, -1.3678e-17],
                                        [-9.6921e-02, 5.3787e-02, 1.2086e-01, 2.2772e-17, -1.2086e-01,
                                         -5.3787e-02, 9.6921e-02],
                                        [-1.7465e-01, 9.6921e-02, 2.1778e-01, 4.1034e-17, -2.1778e-01,
                                         -9.6921e-02, 1.7465e-01],
                                        [-2.1778e-01, 1.2086e-01, 2.7157e-01, 5.1169e-17, -2.7157e-01,
                                         -1.2086e-01, 2.1778e-01]])
        self.filter[23] = torch.Tensor([[2.0126e-01, -1.1169e-01, -2.5097e-01, -4.7287e-17, 2.5097e-01,
                                         1.1169e-01, -2.0126e-01],
                                        [4.9707e-02, -2.7585e-02, -6.1983e-02, -1.1679e-17, 6.1983e-02,
                                         2.7585e-02, -4.9707e-02],
                                        [-1.3928e-01, 7.7292e-02, 1.7367e-01, 3.2724e-17, -1.7367e-01,
                                         -7.7292e-02, 1.3928e-01],
                                        [-2.2338e-01, 1.2397e-01, 2.7855e-01, 5.2485e-17, -2.7855e-01,
                                         -1.2397e-01, 2.2338e-01],
                                        [-1.3928e-01, 7.7292e-02, 1.7367e-01, 3.2724e-17, -1.7367e-01,
                                         -7.7292e-02, 1.3928e-01],
                                        [4.9707e-02, -2.7585e-02, -6.1983e-02, -1.1679e-17, 6.1983e-02,
                                         2.7585e-02, -4.9707e-02],
                                        [2.0126e-01, -1.1169e-01, -2.5097e-01, -4.7287e-17, 2.5097e-01,
                                         1.1169e-01, -2.0126e-01]])
        self.filter[24] = torch.Tensor([[1.7465e-01, -9.6921e-02, -2.1778e-01, -4.1034e-17, 2.1778e-01,
                                         9.6921e-02, -1.7465e-01],
                                        [-9.6921e-02, 5.3787e-02, 1.2086e-01, 2.2772e-17, -1.2086e-01,
                                         -5.3787e-02, 9.6921e-02],
                                        [-2.1778e-01, 1.2086e-01, 2.7157e-01, 5.1169e-17, -2.7157e-01,
                                         -1.2086e-01, 2.1778e-01],
                                        [-4.1034e-17, 2.2772e-17, 5.1169e-17, 9.6413e-33, -5.1169e-17,
                                         -2.2772e-17, 4.1034e-17],
                                        [2.1778e-01, -1.2086e-01, -2.7157e-01, -5.1169e-17, 2.7157e-01,
                                         1.2086e-01, -2.1778e-01],
                                        [9.6921e-02, -5.3787e-02, -1.2086e-01, -2.2772e-17, 1.2086e-01,
                                         5.3787e-02, -9.6921e-02],
                                        [-1.7465e-01, 9.6921e-02, 2.1778e-01, 4.1034e-17, -2.1778e-01,
                                         -9.6921e-02, 1.7465e-01]])
        self.filter[25] = torch.Tensor([[1.3928e-01, -7.7292e-02, -1.7367e-01, -3.2724e-17, 1.7367e-01,
                                         7.7292e-02, -1.3928e-01],
                                        [-2.0126e-01, 1.1169e-01, 2.5097e-01, 4.7287e-17, -2.5097e-01,
                                         -1.1169e-01, 2.0126e-01],
                                        [-4.9707e-02, 2.7585e-02, 6.1983e-02, 1.1679e-17, -6.1983e-02,
                                         -2.7585e-02, 4.9707e-02],
                                        [2.2338e-01, -1.2397e-01, -2.7855e-01, -5.2485e-17, 2.7855e-01,
                                         1.2397e-01, -2.2338e-01],
                                        [-4.9707e-02, 2.7585e-02, 6.1983e-02, 1.1679e-17, -6.1983e-02,
                                         -2.7585e-02, 4.9707e-02],
                                        [-2.0126e-01, 1.1169e-01, 2.5097e-01, 4.7287e-17, -2.5097e-01,
                                         -1.1169e-01, 2.0126e-01],
                                        [1.3928e-01, -7.7292e-02, -1.7367e-01, -3.2724e-17, 1.7367e-01,
                                         7.7292e-02, -1.3928e-01]])
        self.filter[26] = torch.Tensor([[9.6921e-02, -5.3787e-02, -1.2086e-01, -2.2772e-17, 1.2086e-01,
                                         5.3787e-02, -9.6921e-02],
                                        [-2.1778e-01, 1.2086e-01, 2.7157e-01, 5.1169e-17, -2.7157e-01,
                                         -1.2086e-01, 2.1778e-01],
                                        [1.7465e-01, -9.6921e-02, -2.1778e-01, -4.1034e-17, 2.1778e-01,
                                         9.6921e-02, -1.7465e-01],
                                        [6.8391e-17, -3.7954e-17, -8.5282e-17, -1.6069e-32, 8.5282e-17,
                                         3.7954e-17, -6.8391e-17],
                                        [-1.7465e-01, 9.6921e-02, 2.1778e-01, 4.1034e-17, -2.1778e-01,
                                         -9.6921e-02, 1.7465e-01],
                                        [2.1778e-01, -1.2086e-01, -2.7157e-01, -5.1169e-17, 2.7157e-01,
                                         1.2086e-01, -2.1778e-01],
                                        [-9.6921e-02, 5.3787e-02, 1.2086e-01, 2.2772e-17, -1.2086e-01,
                                         -5.3787e-02, 9.6921e-02]])
        self.filter[27] = torch.Tensor([[4.9707e-02, -2.7585e-02, -6.1983e-02, -1.1679e-17, 6.1983e-02,
                                         2.7585e-02, -4.9707e-02],
                                        [-1.3928e-01, 7.7292e-02, 1.7367e-01, 3.2724e-17, -1.7367e-01,
                                         -7.7292e-02, 1.3928e-01],
                                        [2.0126e-01, -1.1169e-01, -2.5097e-01, -4.7287e-17, 2.5097e-01,
                                         1.1169e-01, -2.0126e-01],
                                        [-2.2338e-01, 1.2397e-01, 2.7855e-01, 5.2485e-17, -2.7855e-01,
                                         -1.2397e-01, 2.2338e-01],
                                        [2.0126e-01, -1.1169e-01, -2.5097e-01, -4.7287e-17, 2.5097e-01,
                                         1.1169e-01, -2.0126e-01],
                                        [-1.3928e-01, 7.7292e-02, 1.7367e-01, 3.2724e-17, -1.7367e-01,
                                         -7.7292e-02, 1.3928e-01],
                                        [4.9707e-02, -2.7585e-02, -6.1983e-02, -1.1679e-17, 6.1983e-02,
                                         2.7585e-02, -4.9707e-02]])
        self.filter[28] = torch.Tensor([[0.1260, -0.1820, -0.0450, 0.2020, -0.0450, -0.1820, 0.1260],
                                        [0.1260, -0.1820, -0.0450, 0.2020, -0.0450, -0.1820, 0.1260],
                                        [0.1260, -0.1820, -0.0450, 0.2020, -0.0450, -0.1820, 0.1260],
                                        [0.1260, -0.1820, -0.0450, 0.2020, -0.0450, -0.1820, 0.1260],
                                        [0.1260, -0.1820, -0.0450, 0.2020, -0.0450, -0.1820, 0.1260],
                                        [0.1260, -0.1820, -0.0450, 0.2020, -0.0450, -0.1820, 0.1260],
                                        [0.1260, -0.1820, -0.0450, 0.2020, -0.0450, -0.1820, 0.1260]])
        self.filter[29] = torch.Tensor([[1.7367e-01, -2.5097e-01, -6.1983e-02, 2.7855e-01, -6.1983e-02,
                                         -2.5097e-01, 1.7367e-01],
                                        [1.3928e-01, -2.0126e-01, -4.9707e-02, 2.2338e-01, -4.9707e-02,
                                         -2.0126e-01, 1.3928e-01],
                                        [7.7292e-02, -1.1169e-01, -2.7585e-02, 1.2397e-01, -2.7585e-02,
                                         -1.1169e-01, 7.7292e-02],
                                        [1.0908e-17, -1.5762e-17, -3.8930e-18, 1.7495e-17, -3.8930e-18,
                                         -1.5762e-17, 1.0908e-17],
                                        [-7.7292e-02, 1.1169e-01, 2.7585e-02, -1.2397e-01, 2.7585e-02,
                                         1.1169e-01, -7.7292e-02],
                                        [-1.3928e-01, 2.0126e-01, 4.9707e-02, -2.2338e-01, 4.9707e-02,
                                         2.0126e-01, -1.3928e-01],
                                        [-1.7367e-01, 2.5097e-01, 6.1983e-02, -2.7855e-01, 6.1983e-02,
                                         2.5097e-01, -1.7367e-01]])
        self.filter[30] = torch.Tensor([[0.1605, -0.2319, -0.0573, 0.2574, -0.0573, -0.2319, 0.1605],
                                        [0.0396, -0.0573, -0.0141, 0.0636, -0.0141, -0.0573, 0.0396],
                                        [-0.1111, 0.1605, 0.0396, -0.1781, 0.0396, 0.1605, -0.1111],
                                        [-0.1781, 0.2574, 0.0636, -0.2857, 0.0636, 0.2574, -0.1781],
                                        [-0.1111, 0.1605, 0.0396, -0.1781, 0.0396, 0.1605, -0.1111],
                                        [0.0396, -0.0573, -0.0141, 0.0636, -0.0141, -0.0573, 0.0396],
                                        [0.1605, -0.2319, -0.0573, 0.2574, -0.0573, -0.2319, 0.1605]])
        self.filter[31] = torch.Tensor([[1.3928e-01, -2.0126e-01, -4.9707e-02, 2.2338e-01, -4.9707e-02,
                                         -2.0126e-01, 1.3928e-01],
                                        [-7.7292e-02, 1.1169e-01, 2.7585e-02, -1.2397e-01, 2.7585e-02,
                                         1.1169e-01, -7.7292e-02],
                                        [-1.7367e-01, 2.5097e-01, 6.1983e-02, -2.7855e-01, 6.1983e-02,
                                         2.5097e-01, -1.7367e-01],
                                        [-3.2724e-17, 4.7287e-17, 1.1679e-17, -5.2485e-17, 1.1679e-17,
                                         4.7287e-17, -3.2724e-17],
                                        [1.7367e-01, -2.5097e-01, -6.1983e-02, 2.7855e-01, -6.1983e-02,
                                         -2.5097e-01, 1.7367e-01],
                                        [7.7292e-02, -1.1169e-01, -2.7585e-02, 1.2397e-01, -2.7585e-02,
                                         -1.1169e-01, 7.7292e-02],
                                        [-1.3928e-01, 2.0126e-01, 4.9707e-02, -2.2338e-01, 4.9707e-02,
                                         2.0126e-01, -1.3928e-01]])
        self.filter[32] = torch.Tensor([[0.1111, -0.1605, -0.0396, 0.1781, -0.0396, -0.1605, 0.1111],
                                        [-0.1605, 0.2319, 0.0573, -0.2574, 0.0573, 0.2319, -0.1605],
                                        [-0.0396, 0.0573, 0.0141, -0.0636, 0.0141, 0.0573, -0.0396],
                                        [0.1781, -0.2574, -0.0636, 0.2857, -0.0636, -0.2574, 0.1781],
                                        [-0.0396, 0.0573, 0.0141, -0.0636, 0.0141, 0.0573, -0.0396],
                                        [-0.1605, 0.2319, 0.0573, -0.2574, 0.0573, 0.2319, -0.1605],
                                        [0.1111, -0.1605, -0.0396, 0.1781, -0.0396, -0.1605, 0.1111]])
        self.filter[33] = torch.Tensor([[7.7292e-02, -1.1169e-01, -2.7585e-02, 1.2397e-01, -2.7585e-02,
                                         -1.1169e-01, 7.7292e-02],
                                        [-1.7367e-01, 2.5097e-01, 6.1983e-02, -2.7855e-01, 6.1983e-02,
                                         2.5097e-01, -1.7367e-01],
                                        [1.3928e-01, -2.0126e-01, -4.9707e-02, 2.2338e-01, -4.9707e-02,
                                         -2.0126e-01, 1.3928e-01],
                                        [5.4540e-17, -7.8812e-17, -1.9465e-17, 8.7475e-17, -1.9465e-17,
                                         -7.8812e-17, 5.4540e-17],
                                        [-1.3928e-01, 2.0126e-01, 4.9707e-02, -2.2338e-01, 4.9707e-02,
                                         2.0126e-01, -1.3928e-01],
                                        [1.7367e-01, -2.5097e-01, -6.1983e-02, 2.7855e-01, -6.1983e-02,
                                         -2.5097e-01, 1.7367e-01],
                                        [-7.7292e-02, 1.1169e-01, 2.7585e-02, -1.2397e-01, 2.7585e-02,
                                         1.1169e-01, -7.7292e-02]])
        self.filter[34] = torch.Tensor([[0.0396, -0.0573, -0.0141, 0.0636, -0.0141, -0.0573, 0.0396],
                                        [-0.1111, 0.1605, 0.0396, -0.1781, 0.0396, 0.1605, -0.1111],
                                        [0.1605, -0.2319, -0.0573, 0.2574, -0.0573, -0.2319, 0.1605],
                                        [-0.1781, 0.2574, 0.0636, -0.2857, 0.0636, 0.2574, -0.1781],
                                        [0.1605, -0.2319, -0.0573, 0.2574, -0.0573, -0.2319, 0.1605],
                                        [-0.1111, 0.1605, 0.0396, -0.1781, 0.0396, 0.1605, -0.1111],
                                        [0.0396, -0.0573, -0.0141, 0.0636, -0.0141, -0.0573, 0.0396]])
        self.filter[35] = torch.Tensor([[8.7658e-02, -1.9697e-01, 1.5795e-01, 6.1854e-17, -1.5795e-01,
                                         1.9697e-01, -8.7658e-02],
                                        [8.7658e-02, -1.9697e-01, 1.5795e-01, 6.1854e-17, -1.5795e-01,
                                         1.9697e-01, -8.7658e-02],
                                        [8.7658e-02, -1.9697e-01, 1.5795e-01, 6.1854e-17, -1.5795e-01,
                                         1.9697e-01, -8.7658e-02],
                                        [8.7658e-02, -1.9697e-01, 1.5795e-01, 6.1854e-17, -1.5795e-01,
                                         1.9697e-01, -8.7658e-02],
                                        [8.7658e-02, -1.9697e-01, 1.5795e-01, 6.1854e-17, -1.5795e-01,
                                         1.9697e-01, -8.7658e-02],
                                        [8.7658e-02, -1.9697e-01, 1.5795e-01, 6.1854e-17, -1.5795e-01,
                                         1.9697e-01, -8.7658e-02],
                                        [8.7658e-02, -1.9697e-01, 1.5795e-01, 6.1854e-17, -1.5795e-01,
                                         1.9697e-01, -8.7658e-02]])
        self.filter[36] = torch.Tensor([[1.2086e-01, -2.7157e-01, 2.1778e-01, 8.5282e-17, -2.1778e-01,
                                         2.7157e-01, -1.2086e-01],
                                        [9.6921e-02, -2.1778e-01, 1.7465e-01, 6.8391e-17, -1.7465e-01,
                                         2.1778e-01, -9.6921e-02],
                                        [5.3787e-02, -1.2086e-01, 9.6921e-02, 3.7954e-17, -9.6921e-02,
                                         1.2086e-01, -5.3787e-02],
                                        [7.5908e-18, -1.7056e-17, 1.3678e-17, 5.3563e-33, -1.3678e-17,
                                         1.7056e-17, -7.5908e-18],
                                        [-5.3787e-02, 1.2086e-01, -9.6921e-02, -3.7954e-17, 9.6921e-02,
                                         -1.2086e-01, 5.3787e-02],
                                        [-9.6921e-02, 2.1778e-01, -1.7465e-01, -6.8391e-17, 1.7465e-01,
                                         -2.1778e-01, 9.6921e-02],
                                        [-1.2086e-01, 2.7157e-01, -2.1778e-01, -8.5282e-17, 2.1778e-01,
                                         -2.7157e-01, 1.2086e-01]])
        self.filter[37] = torch.Tensor([[1.1169e-01, -2.5097e-01, 2.0126e-01, 7.8812e-17, -2.0126e-01,
                                         2.5097e-01, -1.1169e-01],
                                        [2.7585e-02, -6.1983e-02, 4.9707e-02, 1.9465e-17, -4.9707e-02,
                                         6.1983e-02, -2.7585e-02],
                                        [-7.7292e-02, 1.7367e-01, -1.3928e-01, -5.4540e-17, 1.3928e-01,
                                         -1.7367e-01, 7.7292e-02],
                                        [-1.2397e-01, 2.7855e-01, -2.2338e-01, -8.7475e-17, 2.2338e-01,
                                         -2.7855e-01, 1.2397e-01],
                                        [-7.7292e-02, 1.7367e-01, -1.3928e-01, -5.4540e-17, 1.3928e-01,
                                         -1.7367e-01, 7.7292e-02],
                                        [2.7585e-02, -6.1983e-02, 4.9707e-02, 1.9465e-17, -4.9707e-02,
                                         6.1983e-02, -2.7585e-02],
                                        [1.1169e-01, -2.5097e-01, 2.0126e-01, 7.8812e-17, -2.0126e-01,
                                         2.5097e-01, -1.1169e-01]])
        self.filter[38] = torch.Tensor([[9.6921e-02, -2.1778e-01, 1.7465e-01, 6.8391e-17, -1.7465e-01,
                                         2.1778e-01, -9.6921e-02],
                                        [-5.3787e-02, 1.2086e-01, -9.6921e-02, -3.7954e-17, 9.6921e-02,
                                         -1.2086e-01, 5.3787e-02],
                                        [-1.2086e-01, 2.7157e-01, -2.1778e-01, -8.5282e-17, 2.1778e-01,
                                         -2.7157e-01, 1.2086e-01],
                                        [-2.2772e-17, 5.1169e-17, -4.1034e-17, -1.6069e-32, 4.1034e-17,
                                         -5.1169e-17, 2.2772e-17],
                                        [1.2086e-01, -2.7157e-01, 2.1778e-01, 8.5282e-17, -2.1778e-01,
                                         2.7157e-01, -1.2086e-01],
                                        [5.3787e-02, -1.2086e-01, 9.6921e-02, 3.7954e-17, -9.6921e-02,
                                         1.2086e-01, -5.3787e-02],
                                        [-9.6921e-02, 2.1778e-01, -1.7465e-01, -6.8391e-17, 1.7465e-01,
                                         -2.1778e-01, 9.6921e-02]])
        self.filter[39] = torch.Tensor([[7.7292e-02, -1.7367e-01, 1.3928e-01, 5.4540e-17, -1.3928e-01,
                                         1.7367e-01, -7.7292e-02],
                                        [-1.1169e-01, 2.5097e-01, -2.0126e-01, -7.8812e-17, 2.0126e-01,
                                         -2.5097e-01, 1.1169e-01],
                                        [-2.7585e-02, 6.1983e-02, -4.9707e-02, -1.9465e-17, 4.9707e-02,
                                         -6.1983e-02, 2.7585e-02],
                                        [1.2397e-01, -2.7855e-01, 2.2338e-01, 8.7475e-17, -2.2338e-01,
                                         2.7855e-01, -1.2397e-01],
                                        [-2.7585e-02, 6.1983e-02, -4.9707e-02, -1.9465e-17, 4.9707e-02,
                                         -6.1983e-02, 2.7585e-02],
                                        [-1.1169e-01, 2.5097e-01, -2.0126e-01, -7.8812e-17, 2.0126e-01,
                                         -2.5097e-01, 1.1169e-01],
                                        [7.7292e-02, -1.7367e-01, 1.3928e-01, 5.4540e-17, -1.3928e-01,
                                         1.7367e-01, -7.7292e-02]])
        self.filter[40] = torch.Tensor([[5.3787e-02, -1.2086e-01, 9.6921e-02, 3.7954e-17, -9.6921e-02,
                                         1.2086e-01, -5.3787e-02],
                                        [-1.2086e-01, 2.7157e-01, -2.1778e-01, -8.5282e-17, 2.1778e-01,
                                         -2.7157e-01, 1.2086e-01],
                                        [9.6921e-02, -2.1778e-01, 1.7465e-01, 6.8391e-17, -1.7465e-01,
                                         2.1778e-01, -9.6921e-02],
                                        [3.7954e-17, -8.5282e-17, 6.8391e-17, 2.6781e-32, -6.8391e-17,
                                         8.5282e-17, -3.7954e-17],
                                        [-9.6921e-02, 2.1778e-01, -1.7465e-01, -6.8391e-17, 1.7465e-01,
                                         -2.1778e-01, 9.6921e-02],
                                        [1.2086e-01, -2.7157e-01, 2.1778e-01, 8.5282e-17, -2.1778e-01,
                                         2.7157e-01, -1.2086e-01],
                                        [-5.3787e-02, 1.2086e-01, -9.6921e-02, -3.7954e-17, 9.6921e-02,
                                         -1.2086e-01, 5.3787e-02]])
        self.filter[41] = torch.Tensor([[2.7585e-02, -6.1983e-02, 4.9707e-02, 1.9465e-17, -4.9707e-02,
                                         6.1983e-02, -2.7585e-02],
                                        [-7.7292e-02, 1.7367e-01, -1.3928e-01, -5.4540e-17, 1.3928e-01,
                                         -1.7367e-01, 7.7292e-02],
                                        [1.1169e-01, -2.5097e-01, 2.0126e-01, 7.8812e-17, -2.0126e-01,
                                         2.5097e-01, -1.1169e-01],
                                        [-1.2397e-01, 2.7855e-01, -2.2338e-01, -8.7475e-17, 2.2338e-01,
                                         -2.7855e-01, 1.2397e-01],
                                        [1.1169e-01, -2.5097e-01, 2.0126e-01, 7.8812e-17, -2.0126e-01,
                                         2.5097e-01, -1.1169e-01],
                                        [-7.7292e-02, 1.7367e-01, -1.3928e-01, -5.4540e-17, 1.3928e-01,
                                         -1.7367e-01, 7.7292e-02],
                                        [2.7585e-02, -6.1983e-02, 4.9707e-02, 1.9465e-17, -4.9707e-02,
                                         6.1983e-02, -2.7585e-02]])
        self.filter[42] = torch.Tensor([[0.0450, -0.1260, 0.1820, -0.2020, 0.1820, -0.1260, 0.0450],
                                        [0.0450, -0.1260, 0.1820, -0.2020, 0.1820, -0.1260, 0.0450],
                                        [0.0450, -0.1260, 0.1820, -0.2020, 0.1820, -0.1260, 0.0450],
                                        [0.0450, -0.1260, 0.1820, -0.2020, 0.1820, -0.1260, 0.0450],
                                        [0.0450, -0.1260, 0.1820, -0.2020, 0.1820, -0.1260, 0.0450],
                                        [0.0450, -0.1260, 0.1820, -0.2020, 0.1820, -0.1260, 0.0450],
                                        [0.0450, -0.1260, 0.1820, -0.2020, 0.1820, -0.1260, 0.0450]])
        self.filter[43] = torch.Tensor([[6.1983e-02, -1.7367e-01, 2.5097e-01, -2.7855e-01, 2.5097e-01,
                                         -1.7367e-01, 6.1983e-02],
                                        [4.9707e-02, -1.3928e-01, 2.0126e-01, -2.2338e-01, 2.0126e-01,
                                         -1.3928e-01, 4.9707e-02],
                                        [2.7585e-02, -7.7292e-02, 1.1169e-01, -1.2397e-01, 1.1169e-01,
                                         -7.7292e-02, 2.7585e-02],
                                        [3.8930e-18, -1.0908e-17, 1.5762e-17, -1.7495e-17, 1.5762e-17,
                                         -1.0908e-17, 3.8930e-18],
                                        [-2.7585e-02, 7.7292e-02, -1.1169e-01, 1.2397e-01, -1.1169e-01,
                                         7.7292e-02, -2.7585e-02],
                                        [-4.9707e-02, 1.3928e-01, -2.0126e-01, 2.2338e-01, -2.0126e-01,
                                         1.3928e-01, -4.9707e-02],
                                        [-6.1983e-02, 1.7367e-01, -2.5097e-01, 2.7855e-01, -2.5097e-01,
                                         1.7367e-01, -6.1983e-02]])
        self.filter[44] = torch.Tensor([[0.0573, -0.1605, 0.2319, -0.2574, 0.2319, -0.1605, 0.0573],
                                        [0.0141, -0.0396, 0.0573, -0.0636, 0.0573, -0.0396, 0.0141],
                                        [-0.0396, 0.1111, -0.1605, 0.1781, -0.1605, 0.1111, -0.0396],
                                        [-0.0636, 0.1781, -0.2574, 0.2857, -0.2574, 0.1781, -0.0636],
                                        [-0.0396, 0.1111, -0.1605, 0.1781, -0.1605, 0.1111, -0.0396],
                                        [0.0141, -0.0396, 0.0573, -0.0636, 0.0573, -0.0396, 0.0141],
                                        [0.0573, -0.1605, 0.2319, -0.2574, 0.2319, -0.1605, 0.0573]])
        self.filter[45] = torch.Tensor([[4.9707e-02, -1.3928e-01, 2.0126e-01, -2.2338e-01, 2.0126e-01,
                                         -1.3928e-01, 4.9707e-02],
                                        [-2.7585e-02, 7.7292e-02, -1.1169e-01, 1.2397e-01, -1.1169e-01,
                                         7.7292e-02, -2.7585e-02],
                                        [-6.1983e-02, 1.7367e-01, -2.5097e-01, 2.7855e-01, -2.5097e-01,
                                         1.7367e-01, -6.1983e-02],
                                        [-1.1679e-17, 3.2724e-17, -4.7287e-17, 5.2485e-17, -4.7287e-17,
                                         3.2724e-17, -1.1679e-17],
                                        [6.1983e-02, -1.7367e-01, 2.5097e-01, -2.7855e-01, 2.5097e-01,
                                         -1.7367e-01, 6.1983e-02],
                                        [2.7585e-02, -7.7292e-02, 1.1169e-01, -1.2397e-01, 1.1169e-01,
                                         -7.7292e-02, 2.7585e-02],
                                        [-4.9707e-02, 1.3928e-01, -2.0126e-01, 2.2338e-01, -2.0126e-01,
                                         1.3928e-01, -4.9707e-02]])
        self.filter[46] = torch.Tensor([[0.0396, -0.1111, 0.1605, -0.1781, 0.1605, -0.1111, 0.0396],
                                        [-0.0573, 0.1605, -0.2319, 0.2574, -0.2319, 0.1605, -0.0573],
                                        [-0.0141, 0.0396, -0.0573, 0.0636, -0.0573, 0.0396, -0.0141],
                                        [0.0636, -0.1781, 0.2574, -0.2857, 0.2574, -0.1781, 0.0636],
                                        [-0.0141, 0.0396, -0.0573, 0.0636, -0.0573, 0.0396, -0.0141],
                                        [-0.0573, 0.1605, -0.2319, 0.2574, -0.2319, 0.1605, -0.0573],
                                        [0.0396, -0.1111, 0.1605, -0.1781, 0.1605, -0.1111, 0.0396]])
        self.filter[47] = torch.Tensor([[2.7585e-02, -7.7292e-02, 1.1169e-01, -1.2397e-01, 1.1169e-01,
                                         -7.7292e-02, 2.7585e-02],
                                        [-6.1983e-02, 1.7367e-01, -2.5097e-01, 2.7855e-01, -2.5097e-01,
                                         1.7367e-01, -6.1983e-02],
                                        [4.9707e-02, -1.3928e-01, 2.0126e-01, -2.2338e-01, 2.0126e-01,
                                         -1.3928e-01, 4.9707e-02],
                                        [1.9465e-17, -5.4540e-17, 7.8812e-17, -8.7475e-17, 7.8812e-17,
                                         -5.4540e-17, 1.9465e-17],
                                        [-4.9707e-02, 1.3928e-01, -2.0126e-01, 2.2338e-01, -2.0126e-01,
                                         1.3928e-01, -4.9707e-02],
                                        [6.1983e-02, -1.7367e-01, 2.5097e-01, -2.7855e-01, 2.5097e-01,
                                         -1.7367e-01, 6.1983e-02],
                                        [-2.7585e-02, 7.7292e-02, -1.1169e-01, 1.2397e-01, -1.1169e-01,
                                         7.7292e-02, -2.7585e-02]])
        self.filter[48] = torch.Tensor([[0.0141, -0.0396, 0.0573, -0.0636, 0.0573, -0.0396, 0.0141],
                                        [-0.0396, 0.1111, -0.1605, 0.1781, -0.1605, 0.1111, -0.0396],
                                        [0.0573, -0.1605, 0.2319, -0.2574, 0.2319, -0.1605, 0.0573],
                                        [-0.0636, 0.1781, -0.2574, 0.2857, -0.2574, 0.1781, -0.0636],
                                        [0.0573, -0.1605, 0.2319, -0.2574, 0.2319, -0.1605, 0.0573],
                                        [-0.0396, 0.1111, -0.1605, 0.1781, -0.1605, 0.1111, -0.0396],
                                        [0.0141, -0.0396, 0.0573, -0.0636, 0.0573, -0.0396, 0.0141]])

        # self.important = torch.Tensor([92.08, 92.41, 92.54, 92.46, 92.63, 92.44, 92.42,
        #                                92.44, 92.43, 92.76, 92.56, 92.74, 92.47, 92.42,
        #                                92.55, 92.80, 92.72, 92.69, 92.52, 92.41, 92.46,
        #                                92.51, 92.61, 92.60, 92.46, 92.42, 92.69, 92.61,
        #                                92.52, 92.53, 92.69, 92.75, 92.74, 92.67, 92.89,
        #                                92.44, 92.69, 92.48, 92.55, 92.81, 92.61, 92.53,
        #                                92.50, 92.53, 92.70, 92.28, 92.56, 92.62, 92.55])

        self.important = torch.Tensor([70.25, 70.05, 70.25, 70.68, 70.24, 70.06, 70.03,
                                       70.43, 70.18, 69.43, 70.18, 69.91, 70.27, 70.35,
                                       70.03, 69.97, 70.07, 70.43, 70.26, 70.30, 70.31,
                                       70.42, 70.03, 70.46, 70.39, 70.19, 70.23, 70.24,
                                       69.92, 70.17, 70.02, 69.90, 69.90, 70.50, 70.62,
                                       70.27, 70.21, 70.30, 70.22, 70.65, 70.62, 70.37,
                                       69.83, 70.10, 70.30, 70.07, 70.32, 70.23, 70.02])

    def get_filter(self, idx):
        if isinstance(idx, int):
            return self.filter[idx]
        else:
            idx = idx.to(self.filter.device)
            out = self.filter.index_select(dim=0, index=idx)
            return out

    def get_topk(self, topk):
        _, ids = torch.topk(self.important, topk)

        return ids


class DCT3x3(nn.Module):
    def __init__(self):
        super(DCT3x3, self).__init__()
        self.filter = torch.zeros(9, 3, 3).cuda()
        self.freq_num = 9
        self.freq_range = 3

        self.filter[0] = torch.Tensor([[0.3333, 0.3333, 0.3333],
                                       [0.3333, 0.3333, 0.3333],
                                       [0.3333, 0.3333, 0.3333]])
        self.filter[1] = torch.Tensor([[4.0825e-01, 4.0825e-01, 4.0825e-01],
                                       [2.8865e-17, 2.8865e-17, 2.8865e-17],
                                       [-4.0825e-01, -4.0825e-01, -4.0825e-01]])
        self.filter[2] = torch.Tensor([[0.2357, 0.2357, 0.2357],
                                       [-0.4714, -0.4714, -0.4714],
                                       [0.2357, 0.2357, 0.2357]])
        self.filter[3] = torch.Tensor([[4.0825e-01, 2.8865e-17, -4.0825e-01],
                                       [4.0825e-01, 2.8865e-17, -4.0825e-01],
                                       [4.0825e-01, 2.8865e-17, -4.0825e-01]])
        self.filter[4] = torch.Tensor([[5.0000e-01, 3.5353e-17, -5.0000e-01],
                                       [3.5353e-17, 2.4996e-33, -3.5353e-17],
                                       [-5.0000e-01, -3.5353e-17, 5.0000e-01]])
        self.filter[5] = torch.Tensor([[2.8868e-01, 2.0411e-17, -2.8868e-01],
                                       [-5.7735e-01, -4.0822e-17, 5.7735e-01],
                                       [2.8868e-01, 2.0411e-17, -2.8868e-01]])
        self.filter[6] = torch.Tensor([[0.2357, -0.4714, 0.2357],
                                       [0.2357, -0.4714, 0.2357],
                                       [0.2357, -0.4714, 0.2357]])
        self.filter[7] = torch.Tensor([[2.8868e-01, -5.7735e-01, 2.8868e-01],
                                       [2.0411e-17, -4.0822e-17, 2.0411e-17],
                                       [-2.8868e-01, 5.7735e-01, -2.8868e-01]])
        self.filter[8] = torch.Tensor([[0.1667, -0.3333, 0.1667],
                                       [-0.3333, 0.6667, -0.3333],
                                       [0.1667, -0.3333, 0.1667]])

        self.important = None

    def get_filter(self, idx):
        if isinstance(idx, int):
            return self.filter[idx]
        else:
            return torch.index_select(self.filter, 0, idx)

    def get_topk(self, topk):
        _, ids = torch.topk(self.important, topk)

        return ids


def spike_activation(x, ste=False, temp=1.0):
    out_s = torch.gt(x, 0.5)
    if ste:
        out_bp = torch.clamp(x, 0, 1)
    else:
        out_bp = torch.clamp(x, 0, 1)
        out_bp = (torch.tanh(temp * (out_bp - 0.5)) + np.tanh(temp * 0.5)) / (2 * (np.tanh(temp * 0.5)))
    return (out_s.float() - out_bp).detach() + out_bp


def MPR(s, thresh):
    s[s > 1.] = s[s > 1.] ** (1.0 / 3)
    s[s < 0.] = -(-(s[s < 0.] - 1.)) ** (1.0 / 3) + 1.
    s[(0. < s) & (s < 1.)] = 0.5 * torch.tanh(3. * (s[(0. < s) & (s < 1.)] - thresh)) / np.tanh(3. * (thresh)) + 0.5

    return s


def gradient_scale(x, scale):
    yout = x
    ygrad = x * scale
    y = (yout - ygrad).detach() + ygrad

    return y


def mem_update(bn, x_in, mem, v_th, decay, grad_scale=1., temp=1.0):
    mem = mem * decay + x_in
    mem_bn = mem

    spike = spike_activation(mem_bn / v_th, temp=temp)
    mem = mem * (1 - spike)

    return mem, spike


class LIFAct(nn.Module):

    def __init__(self, step, channel):
        super(LIFAct, self).__init__()
        self.step = step
        self.v_th = 1.0
        self.temp = 3.0
        self.grad_scale = 0.1
        self.bn = nn.BatchNorm2d(channel)

    def forward(self, x):
        if self.grad_scale is None:
            self.grad_scale = 1 / math.sqrt(x[0].numel() * self.step)
        u = torch.zeros_like(x[0])
        out = []
        for i in range(self.step):
            u, out_i = mem_update(bn=self.bn, x_in=x[i], mem=u, v_th=self.v_th,
                                  grad_scale=self.grad_scale, decay=0.25, temp=self.temp)
            out += [out_i]

        out = torch.stack(out)
        return out


class FreConv(nn.Module):
    def __init__(self, c, reduction, k=1, p=0):
        super(FreConv, self).__init__()
        if reduction == 1:
            self.freq_attention = nn.Sequential(
                nn.Conv2d(c, 1, kernel_size=k, padding=p, bias=False),
            )
        else:
            self.freq_attention = nn.Sequential(
                nn.Conv2d(c, c // reduction, kernel_size=k, bias=False, padding=p),
                nn.ReLU(),
                nn.Conv2d(c // reduction, 1, kernel_size=k, padding=p, bias=False)
            )

    def forward(self, x):

        return self.freq_attention(x)


class DCTSA(nn.Module):
    def __init__(self, freq_num, channel, step, reduction=1, groups=1, select_method='all'):
        super(DCTSA, self).__init__()
        self.freq_num = freq_num
        self.channel = channel
        self.reduction = reduction
        self.select_method = select_method
        self.groups = groups
        self.step = step

        if freq_num == 64:
            self.dct_filter = DCT8x8()
            self.p = int((self.dct_filter.freq_range - 1) / 2)
        elif freq_num == 49:
            self.dct_filter = DCT7x7()
            self.p = int((self.dct_filter.freq_range - 1) / 2)
        elif freq_num == 9:
            self.dct_filter = DCT3x3()
            self.p = int((self.dct_filter.freq_range - 1) / 2)

        if self.select_method == 'all':
            self.dct_c = self.dct_filter.freq_num
        elif 's' in self.select_method:
            self.dct_c = 1
        elif 'top' in self.select_method:
            self.dct_c = int(self.select_method.replace('top', ''))

        self.freq_attention = FreConv(self.dct_c, reduction=reduction, k=7, p=3)
        self.sigmoid = nn.Sigmoid()

        # cahnnel select
        self.avg_pool_c = nn.AdaptiveAvgPool3d((None, 1, 1))
        self.max_pool_c = nn.AdaptiveMaxPool3d((None, 1, 1))
        self.register_parameter('alpha', nn.Parameter(torch.FloatTensor([0.5])))
        self.register_parameter('beta', nn.Parameter(torch.FloatTensor([0.5])))

        # self.fc_c = nn.Linear(channel, channel, bias=False)
        self.fc_t = nn.Linear(step, step, bias=False)

        self.register_parameter('t', nn.Parameter(torch.FloatTensor([0.6])))  # m
        self.register_parameter('s', nn.Parameter(torch.FloatTensor([0.5])))  # n
        self.register_parameter('x', nn.Parameter(torch.FloatTensor([1])))

        self.register_parameter('t_scale', nn.Parameter(torch.FloatTensor([1])))
        self.register_parameter('s_scale', nn.Parameter(torch.FloatTensor([1])))

    def forward(self, x):
        t, b, c, h, w = x.shape
        x = rearrange(x, 't b c h w -> b t c h w')
        avg_map = self.avg_pool_c(x)  # (b, t, c, 1, 1)
        max_map = self.max_pool_c(x)

        map_add = self.alpha * avg_map + self.beta * max_map

        # time branch
        # map_fusion_t = self.fc_t(map_add)   # (b, t, c, 1, 1)
        map_add = rearrange(map_add, 'b t c 1 1 -> b c t')
        # map_fusion_t = self.fc_t(map_add.squeeze().transpose(1, 2)).transpose(1, 2) # (b, c, t) -> (b, t, c)
        map_fusion_t = self.fc_t(map_add).transpose(1, 2)  # (b, c, t) -> (b, t, c)

        ## time
        t_mean_sig = self.sigmoid(torch.mean(map_fusion_t, dim=2))  # (b, t)
        t_mean_sig = rearrange(t_mean_sig, 'b t -> b t 1 1 1')
        t_mean_sig = t_mean_sig.repeat(1, 1, c, h, w)
        x_t = x * t_mean_sig + x  # (b, t, c, h, w)

        ## sptial
        if self.select_method == 'all':
            dct_weight = self.dct_filter.filter

            dct_weight = dct_weight.unsqueeze(1)
            dct_weight = dct_weight.repeat(1, self.channel, 1, 1)  # * self.step

        elif 's' in self.select_method:
            filter_id = int(self.select_method.replace('s', ''))
            dct_weight = self.dct_filter.get_filter(filter_id)

            dct_weight = dct_weight.unsqueeze(0).unsqueeze(0)

            dct_weight = dct_weight.repeat(1, self.channel, 1, 1)

        elif 'top' in self.select_method:
            filter_id = self.dct_filter.get_topk(self.dct_c)
            dct_weight = self.dct_filter.get_filter(filter_id)
            dct_weight = dct_weight.unsqueeze(1)
            dct_weight = dct_weight.repeat(1, self.channel, 1, 1)

        dct_bias = torch.zeros(self.dct_c).to(dct_weight.device)
        dct_feature = F.conv2d(torch.mean(x_t, dim=1), dct_weight, dct_bias, stride=1,
                               padding=self.p)  # (b, dct_c, h, w)
        dct_feature = self.freq_attention(dct_feature)  # （b, 1, h, w)

        dct_feature = dct_feature.unsqueeze(1)  # (b, 1, 1, h, w)
        dct_feature = dct_feature.repeat(1, t, c, 1, 1)  # (b, t, c, h, w)
        x_s = x_t * self.sigmoid(dct_feature) + x_t

        x = (x_t * self.t + x_s * self.s) / 2
        x = rearrange(x, 'b t c h w -> t b c h w')

        return x


if __name__ == '__main__':
    block = DCTSA(freq_num=9, channel=3, step=10, reduction=1).to('cuda')

    # (time_steps, batch_size, channels, height, width)
    input_tensor = torch.rand(10, 4, 3, 16, 16).to('cuda')

    output = block(input_tensor)

    print(f"输入张量形状: {input_tensor.size()}")
    print(f"输出张量形状: {output.size()}")