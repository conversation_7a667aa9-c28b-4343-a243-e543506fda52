import torch
import torch.nn as nn
import torch.nn.functional as F

"""neurips2023
捕获语义信息对于准确的长期时间序列预测至关重要，这涉及对全局和局部相关性进行建模，以及发现长期和短期重复模式。以前的研究已经部分解决了这些问题，但无法同时解决所有问题。
同时，它们的时间和内存复杂度对于长期预测来说仍然不够低。为了应对捕获不同类型语义信息的挑战，我们提出了一种新颖的水波信息传输 (WIT) 框架。该框架通过双粒度信息传输捕获长期和短期重复模式。
它还通过使用水平垂直门控选择单元 (HVGSU) 递归融合和选择信息来建模全局和局部相关性。
我们提出的方法称为水波信息传输和循环加速网络 (WITRAN)，在四个基准数据集上的实验表明，在长期和超长期时间序列预测任务上，其性能分别比最先进的方法高出 5.80% 和 14.28%。
"""

class HVGSU(nn.Module):
    def __init__(self, input_size, hidden_size):
        super(HVGSU, self).__init__()
        self.hidden_size = hidden_size
        self.W = nn.Parameter(torch.empty(6 * hidden_size, input_size + 2 * hidden_size))
        self.B = nn.Parameter(torch.empty(6 * hidden_size))
        self.reset_parameters()

    def reset_parameters(self):
        stdv = 1.0 / self.hidden_size
        self.W.data.uniform_(-stdv, stdv)
        self.B.data.uniform_(-stdv, stdv)

    def forward(self, hidden_slice_row, hidden_slice_col, input_slice, batch_size, slice_idx, total_slices):
        # Linear transformation
        gate_input = torch.cat([hidden_slice_row, hidden_slice_col, input_slice], dim=-1)
        gate = F.linear(gate_input, self.W)

        # Add bias only for certain slices
        if slice_idx < total_slices:
            gate[:batch_size * (slice_idx + 1), :] += self.B

        # Split and apply gate functions
        sigmod_gate, tanh_gate = torch.split(gate, 4 * self.hidden_size, dim=-1)
        sigmod_gate = torch.sigmoid(sigmod_gate)
        tanh_gate = torch.tanh(tanh_gate)

        update_gate_row, output_gate_row, update_gate_col, output_gate_col = sigmod_gate.chunk(4, dim=-1)
        input_gate_row, input_gate_col = tanh_gate.chunk(2, dim=-1)

        # Update hidden states
        hidden_slice_row = torch.tanh(
            (1 - update_gate_row) * hidden_slice_row + update_gate_row * input_gate_row) * output_gate_row
        hidden_slice_col = torch.tanh(
            (1 - update_gate_col) * hidden_slice_col + update_gate_col * input_gate_col) * output_gate_col

        return hidden_slice_row#, hidden_slice_col


if __name__ == '__main__':
    input_size = 8  # input size
    hidden_size = 8  # hidden size
    batch_size = 4  # batch size
    slice_len = 10  # number of slices

    block = HVGSU(input_size, hidden_size)

    input_slice = torch.rand(batch_size, input_size)
    hidden_slice_row = torch.zeros(batch_size, hidden_size)
    hidden_slice_col = torch.zeros(batch_size, hidden_size)

    output_row, output_col = block(hidden_slice_row, hidden_slice_col, input_slice, batch_size, 0, slice_len)

    print("Input size:", input_slice.size())
    print("Output row size:", output_row.size())
    print("Output col size:", output_col.size())