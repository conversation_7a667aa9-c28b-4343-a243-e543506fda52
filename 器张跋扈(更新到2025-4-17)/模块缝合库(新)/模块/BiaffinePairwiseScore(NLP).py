import torch
import torch.nn as nn
from torch.nn.parameter import Parameter
from torch.nn import ReLU

"""http://github.com/SCUT-CCNL/MGSN
文档级生物医学关系提取旨在提取整个文档中实体的多次提及之间的关系。然而，大多数方法都存在由众多生物医学实体和句子间关系引起的长距离上下文依赖性和复杂语义的问题。
在本文中，我们提出了一种用于文档级关系提取的多粒度顺序网络 (MGSN) 来解决上述问题。所提出的方法通过积累文档级信息和实体级信息（包括全局和局部实体信息）来学习提取文档级实体关系。
此外，一些反映目标实体关系的目标实体对可以通过基于 CNN 的双仿射结构提取并得到更多关注。在三个文档级生物医学数据集上的实验结果证明了所提模型的有效性。
"""


class BiaffinePairwiseScore(nn.Module):
    def __init__(self, hidden_size, label_num):
        super(BiaffinePairwiseScore, self).__init__()
        self.conv1 = nn.Conv1d(hidden_size, 200, 7, padding=3)
        self.conv2 = nn.Conv1d(hidden_size, 200, 7, padding=3)
        self.conv3 = nn.Conv1d(200, 200, 7, padding=3)
        self.conv4 = nn.Conv1d(200, 200, 7, padding=3)

        self.linear = nn.Linear(hidden_size, label_num)
        self.bilstm = nn.LSTM(hidden_size, int(hidden_size / 2), batch_first=True, bidirectional=True)

        self.label_num = label_num
        self.hidden_size = hidden_size
        self.seq_length = 0
        self.weight = Parameter(torch.FloatTensor(200, label_num * 200))
        self.activation = ReLU()
        self.reset_parameters()

    def reset_parameters(self):
        nn.init.kaiming_uniform_(self.weight)

    def forward(self, seq, ep_dist):
        assert seq.ndim == 3
        seq_permute = seq.permute(0, 2, 1)
        self.seq_length = seq.shape[1]

        head = self.conv3(self.activation(self.conv1(seq_permute)))
        tail = self.conv4(self.activation(self.conv2(seq_permute)))
        head = head.permute(0, 2, 1)

        affine = torch.matmul(head, self.weight)  # size is (batch, seq_length, label_num, 200)
        bi_affine = torch.matmul(torch.reshape(affine, (-1, self.seq_length * self.label_num, 200)), tail)
        bi_affine = torch.reshape(bi_affine, (-1, self.seq_length, self.label_num, self.seq_length))
        bi_affine = bi_affine.permute(0, 1, 3, 2)

        ep_dist = ep_dist.unsqueeze(3)
        local_matrix = bi_affine + ep_dist
        local_result = torch.logsumexp(local_matrix, (1, 2))

        global_sequence, (h, c) = self.bilstm(seq)
        global_result = self.linear(global_sequence[:, 0, :])
        output = local_result + global_result

        return output, local_matrix


if __name__ == '__main__':
    batch_size = 2
    seq_length = 10
    hidden_size = 16
    label_num = 5

    block = BiaffinePairwiseScore(hidden_size, label_num)

    input_seq = torch.rand(batch_size, seq_length, hidden_size)
    ep_dist = torch.rand(batch_size, seq_length, seq_length)

    output, local_matrix = block(input_seq, ep_dist)

    print("input_seq size:", input_seq.size())
    print("ep_dist size:", ep_dist.size())
    print("output size:", output.size())
    print("local_matrix size:", local_matrix.size())
