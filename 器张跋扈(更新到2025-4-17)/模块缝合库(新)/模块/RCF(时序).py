# import torch
# import torch.nn as nn
#
# class RecurrentCycle(torch.nn.Module):
#     # Thanks for the contribution of wayhoww.
#     # The new implementation uses index arithmetic with modulo to directly gather cyclic data in a single operation,
#     # while the original implementation manually rolls and repeats the data through looping.
#     # It achieves a significant speed improvement (2x ~ 3x acceleration).
#     # See https://github.com/ACAT-SCUT/CycleNet/pull/4 for more details.
#     def __init__(self, cycle_len, channel_size):
#         super(RecurrentCycle, self).__init__()
#         self.cycle_len = cycle_len
#         self.channel_size = channel_size
#         self.data = torch.nn.Parameter(torch.zeros(cycle_len, channel_size), requires_grad=True)
#
#     def forward(self, index, length):
#         gather_index = (index.view(-1, 1) + torch.arange(length, device=index.device).view(1, -1)) % self.cycle_len
#         return self.data[gather_index]
#
#
# if __name__ == '__main__':
#     # 定义周期长度和通道数
#     cycle_len = 12
#     channel_size = 5
#
#     # 初始化RecurrentCycle模块
#     block = RecurrentCycle(cycle_len=cycle_len, channel_size=channel_size)
#
#     # 测试数据
#     batch_size = 4  # 批次大小
#     seq_len = 10  # 每个序列的长度
#     index = torch.randint(0, cycle_len, (batch_size,))  # 随机生成批次的周期起始索引
#
#     # 前向传播
#     output = block(index, seq_len)
#
#     print("输入索引的形状:", index.size())  # (batch_size,)
#     print("输出的形状:", output.size())  # (batch_size, seq_len, channel_size)


"""(NeurIPS 2024)CycleNet: Enhancing Time Series Forecasting through Modeling Periodic Patterns
时间序列数据中存在的稳定周期模式是进行长期预测的基础。在本文中，我们率先探索了显式建模这种周期性，以提高模型在长期时间序列预测 （LTSF） 任务中的性能。
具体来说，我们介绍了残差周期预测 （RCF） 技术，该技术利用可学习的循环来模拟序列中固有的周期性模式，然后对建模周期的残余成分进行预测。将 RCF 与线性层或浅层 MLP 相结合，形成了本文提出的简单而强大的方法，称为 CycleNet。
CycleNet 在包括电力、天气和能源在内的多个领域实现了最先进的预测精度，同时通过减少 90% 以上的所需参数数量来提供显着的效率优势
。此外，作为一种新颖的即插即用技术，RCF 还可以显著提高现有模型的预测精度，包括 PatchTST 和 iTransformer。
"""


# 使用示例
import torch
import torch.nn as nn

class RecurrentCycle(torch.nn.Module):
    def __init__(self, cycle_len, channel_size):
        super(RecurrentCycle, self).__init__()
        self.cycle_len = cycle_len
        self.channel_size = channel_size
        self.data = torch.nn.Parameter(torch.zeros(cycle_len, channel_size), requires_grad=True)

    def forward(self, index, length):
        gather_index = (index.view(-1, 1) + torch.arange(length, device=index.device).view(1, -1)) % self.cycle_len
        return self.data[gather_index]

class TimeSeriesModel(nn.Module):
    def __init__(self, seq_len, pred_len, cycle_len, channel_size, model_type='linear', d_model=64):
        super(TimeSeriesModel, self).__init__()

        self.seq_len = seq_len
        self.pred_len = pred_len
        self.cycle_len = cycle_len
        self.channel_size = channel_size
        self.model_type = model_type
        self.d_model = d_model

        # RecurrentCycle module
        self.cycleQueue = RecurrentCycle(cycle_len=self.cycle_len, channel_size=self.channel_size)

        # Prediction model (could be Linear or MLP)
        if self.model_type == 'linear':
            self.model = nn.Linear(self.seq_len, self.pred_len)
        elif self.model_type == 'mlp':
            self.model = nn.Sequential(
                nn.Linear(self.seq_len, self.d_model),
                nn.ReLU(),
                nn.Linear(self.d_model, self.pred_len)
            )

    def forward(self, x, cycle_index):
        # x: (batch_size, seq_len, channel_size), cycle_index: (batch_size,

        # Remove the cycle of the input data
        x = x - self.cycleQueue(cycle_index, self.seq_len)

        # Perform prediction
        y = self.model(x.permute(0, 2, 1)).permute(0, 2, 1)

        # Add back the cycle of the output data
        y = y + self.cycleQueue((cycle_index + self.seq_len) % self.cycle_len, self.pred_len)

        return y

# 示例：创建并运行模型
if __name__ == '__main__':
    batch_size = 4
    seq_len = 10
    pred_len = 10
    cycle_len = 20
    channel_size = 5
    model_type = 'linear'

    model = TimeSeriesModel(seq_len=seq_len, pred_len=pred_len, cycle_len=cycle_len, channel_size=channel_size, model_type=model_type)

    # (batch_size, seq_len, channel_size)
    input_tensor = torch.rand(batch_size, seq_len, channel_size)
    # 创建索引 (batch_size,)
    cycle_index = torch.randint(0, cycle_len, (batch_size,))

    output = model(input_tensor, cycle_index)

    print("输入的形状:", input_tensor.size())
    print("输出的形状:", output.size())

