from torch import Tensor
from torch import nn, sum
import torch
from torch.nn import init

"""GEA(Graph External Attention)ICML2024
Transformer架构最近在图表示学习领域获得了相当大的关注，因为它自然地克服了图神经网络（GNN）的几个限制，具有定制的注意力机制或位置和结构编码。
尽管取得了一些进展，但现有的工作往往忽略了图的外部信息，特别是图之间的相关性。
直观地说，具有相似结构的图形应该具有相似的表示。因此，我们提出了图外部注意力（Graph External Attention，GEA）
一种新型的注意力机制，它利用多个外部节点/边缘键值单元来隐式捕获图间相关性。
在此基础上，我们设计了一种有效的架构，称为图外部注意力增强转换器（GEAET），该架构集成了局部结构和全局交互信息，
以实现更全面的图表示。在基准数据集上的广泛实验表明，GEAET实现了最先进的实证性能。
"""


def external_norm(attn):
    softmax = nn.Softmax(dim=0)  # N
    attn = softmax(attn)  # bs,n,S
    attn = attn /sum(attn, dim=2, keepdim=True)  # bs,n,S
    return attn


class DNorm(nn.Module):
    def __init__(
            self,
            dim1=0 ,dim2=2
    ):
        super().__init__()
        self.dim1 =dim1
        self.dim2 =dim2
        self.softmax = nn.Softmax(dim=self.dim1)  # N

    def forward(self, attn: Tensor) -> Tensor:
        # softmax = nn.Softmax(dim=0)  # N
        attn = self.softmax(attn)  # bs,n,S
        attn = attn /sum(attn, dim=self.dim2, keepdim=True)  # bs,n,S
        return attn

class GEANet(nn.Module):

    def __init__(
            self, dim, GEANet_cfg):
        super().__init__()


        self.dim = dim
        self.external_num_heads = GEANet_cfg.n_heads
        self.use_shared_unit = GEANet_cfg.shared_unit
        self.use_edge_unit = GEANet_cfg.edge_unit
        self.unit_size = GEANet_cfg.unit_size

        # self.q_Linear = nn.Linear(in_dim, gconv_dim - dim_pe)
        self.node_U1 = nn.Linear(self.unit_size, self.unit_size)
        self.node_U2 = nn.Linear(self.unit_size, self.unit_size)

        assert self.unit_size * self.external_num_heads == self.dim, "dim must be divisible by external_num_heads"

        # nn.init.xavier_normal_(self.node_m1.weight, gain=1)
        # nn.init.xavier_normal_(self.node_m2.weight, gain=1)
        if  self.use_edge_unit:
            self.edge_U1 = nn.Linear(self.unit_size, self.unit_size)
            self.edge_U2 = nn.Linear(self.unit_size, self.unit_size)
            if self.use_shared_unit:
                self.share_U = nn.Linear(dim, dim)

            # nn.init.xavier_normal_(self.edge_m1.weight, gain=1)
            # nn.init.xavier_normal_(self.edge_m2.weight, gain=1)
            # nn.init.xavier_normal_(self.share_m.weight, gain=1)
        self.norm = DNorm()

        # self.init_weights()


    def init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                init.normal_(m.weight, std=0.001)
                if m.bias is not None:
                    init.constant_(m.bias, 0)


    def forward(self, node_x ,edge_attr = None) -> Tensor:
        if self.use_shared_unit:
            node_x = self.share_U(node_x)
            edge_attr = self.share_U(edge_attr)
        # x : N x 64
        # External attention
        N, d, head = node_x.size()[0], node_x.size()[1], self.external_num_heads
        node_out = node_x.reshape(N, head ,-1)  # Q * 4（head）  ：  N x 16 x 4(head)
        # node_out = node_out.transpose(1, 2)  # (N, 16, 4) -> (N, 4, 16)
        node_out = self.node_U1(node_out)
        attn = self.norm(node_out)  # 行列归一化  N x 16 x 4
        node_out = self.node_U2(attn)
        node_out = node_out.reshape(N, -1)

        if self.use_edge_unit:

            N, d, head = edge_attr.size()[0], edge_attr.size()[1], self.external_num_heads
            edge_out = edge_attr.reshape(N, -1, head)  # Q * 4（head）  ：  N x 16 x 4(head)
            edge_out = edge_out.transpose(1, 2)  # (N, 16, 4) -> (N, 4, 16)
            edge_out = self.edge_U1(edge_out)
            attn = self.norm(edge_out)  # 行列归一化  N x 16 x 4
            edge_out = self.edge_U2(attn)
            edge_out = edge_out.reshape(N, -1)
        else:
            edge_out = edge_attr

        return node_out ,edge_out





class GEANetConfig:
    def __init__(self, n_heads, shared_unit, edge_unit, unit_size):
        self.n_heads = n_heads
        self.shared_unit = shared_unit
        self.edge_unit = edge_unit
        self.unit_size = unit_size


if __name__ == '__main__':
    config = GEANetConfig(
        n_heads=4,
        shared_unit=True,
        edge_unit=True,
        unit_size=16
    )

    dim = 64

    block = GEANet(dim, config)

    node_input = torch.rand(10, dim)
    edge_input = torch.rand(10, dim)

    node_output, edge_output = block(node_input, edge_input)

    print("Node input size:", node_input.size())
    print("Node output size:", node_output.size())
    print("Edge input size:", edge_input.size())
    print("Edge output size:", edge_output.size())

