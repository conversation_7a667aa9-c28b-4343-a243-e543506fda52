import torch
import torch.nn as nn
"""
CV缝合救星魔改创新2：结合空间注意力机制的背景分析及改进
一、背景
传统的SE模块（Squeeze-and-Excitation Block）主要聚焦于通道维度的特征重要性，通过全局平均池化提取
每个通道的全局信息，生成通道注意力权重。然而，SE模块完全忽略了空间维度的特征分布，存在以下几个问题：
1. 空间特征丢失：SE模块的全局平均池化将每个通道的二维空间特征压缩为一个标量，仅保留通道级的全局信息，
无法感知输入特征图在空间维度（height × width） 上的细粒度特征分布。
2. 空间上下文依赖性弱：通道注意力机制只关注通道之间的全局重要性权重，忽视了同一通道内不同位置的激活强
度差异。对于需要感知物体边界、纹理和结构的任务，这种忽视可能导致关键特征点丢失。
3. 通道与空间协同不足：通道注意力机制擅长选择“重要通道”，但无法根据图像中的空间特征动态调整权重，从而
限制了特征选择的灵活性。

二、改进方法
1. 融合通道和空间注意力：在通道注意力计算完成后，进一步对特征图在空间维度上应用注意力机制，使模型能够动
态关注输入特征图中最重要的位置。
2. 空间权重生成方式：使用全局平均池化（Global Average Pooling）和全局最大池化（Global Max Pooling）
生成两个空间特征图，分别表示全局的均值和最强激活点。随后通过一个卷积层聚合这两个空间特征图，生成空间权重。
3. 通道-空间联合加权：先通过通道注意力生成通道权重，再通过空间注意力生成空间权重。最终，模型对输入特征进
行联合加权，从通道和空间两个维度提升特征选择能力。

三、改进后的优势
1. 增强空间特征表达：通过空间注意力机制，模型可以动态关注输入特征图中不同位置的重要性，尤其是在需要捕获物
体边缘或复杂纹理的任务中显著提升性能。
2. 通道与空间互补：通道注意力负责选择重要通道，而空间注意力负责在通道内动态选择关键位置，两者结合可以更全面
地挖掘输入特征中的有效信息。
3. 轻量化与效率：空间注意力仅在空间维度计算注意力权重，计算复杂度低，且与通道注意力分阶段进行，不增加显著的
模型参数量。
"""
class SEWithSpatialAttention(nn.Module):
    def __init__(self, in_channels, reduction=16):
        """
        通道和空间注意力结合的SE模块
        :param in_channels: 输入的通道数
        :param reduction: 压缩比例
        """
        super(SEWithSpatialAttention, self).__init__()
        # 通道注意力部分
        self.global_avg_pool = nn.AdaptiveAvgPool2d(1)  # 全局平均池化
        self.conv1 = nn.Conv2d(in_channels, in_channels // reduction, kernel_size=1, bias=False)  # 降维
        self.relu = nn.ReLU(inplace=True)  # ReLU 激活
        self.conv2 = nn.Conv2d(in_channels // reduction, in_channels, kernel_size=1, bias=False)  # 恢复维度
        self.sigmoid_channel = nn.Sigmoid()  # Sigmoid 激活生成通道权重

        # 空间注意力部分
        self.conv_spatial = nn.Conv2d(2, 1, kernel_size=7, padding=3, bias=False)  # 使用7x7卷积捕获空间信息
        self.sigmoid_spatial = nn.Sigmoid()  # Sigmoid 激活生成空间权重

    def forward(self, x):
        # 通道注意力部分
        b, c, _, _ = x.size()
        y = self.global_avg_pool(x)
        y = self.conv1(y)
        y = self.relu(y)
        y = self.conv2(y)
        y_channel = self.sigmoid_channel(y)  # 通道权重
        x_channel = x * y_channel  # 按通道加权

        # 空间注意力部分
        avg_out = torch.mean(x_channel, dim=1, keepdim=True)  # 按通道求平均
        max_out, _ = torch.max(x_channel, dim=1, keepdim=True)  # 按通道求最大值
        y_spatial = torch.cat([avg_out, max_out], dim=1)  # 将平均值和最大值拼接
        y_spatial = self.conv_spatial(y_spatial)
        y_spatial = self.sigmoid_spatial(y_spatial)  # 空间权重
        return x_channel * y_spatial  # 同时加权通道和空间

if __name__ == "__main__":
    # 输入张量，形状为 [batch_size, channels, height, width]
    input_tensor = torch.randn(8, 64, 32, 32)  # 批量大小8，通道数64，特征图尺寸32x32
    se_block = SEWithSpatialAttention(in_channels=64, reduction=16)
    output_tensor = se_block(input_tensor)  # 前向传播
    print("Input shape:", input_tensor.shape)
    print("Output shape:", output_tensor.shape)