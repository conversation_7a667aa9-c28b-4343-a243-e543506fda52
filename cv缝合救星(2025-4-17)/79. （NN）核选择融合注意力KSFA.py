import torch
import torch.nn as nn

# 哔哩哔哩：CV缝合救星
class KernelSelectiveFusionAttention(nn.Module):
    def __init__(self, dim, r=16, L=32):
        super().__init__()
        d = max(dim // r, L)
        self.conv0 = nn.Conv2d(dim, dim, 3, padding=1, groups=dim)
        self.conv_spatial = nn.Conv2d(dim, dim, 5, stride=1, padding=4, groups=dim, dilation=2)
        self.conv1 = nn.Conv2d(dim, dim // 2, 1)
        self.conv2 = nn.Conv2d(dim, dim // 2, 1)
        self.conv_squeeze = nn.Conv2d(2, 2, 7, padding=3)
        self.conv = nn.Conv2d(dim // 2, dim, 1)

        self.global_pool = nn.AdaptiveAvgPool2d(1)
        self.global_maxpool = nn.AdaptiveMaxPool2d(1)
        self.fc1 = nn.Sequential(
            nn.Conv2d(dim, d, 1, bias=False),
            nn.BatchNorm2d(d),
            nn.ReLU(inplace=True)
        )
        self.fc2 = nn.Conv2d(d, dim, 1, 1, bias=False)
        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        batch_size = x.size(0)
        dim = x.size(1) # 哔哩哔哩：CV缝合救星
        attn1 = self.conv0(x)  # conv_3*3
        attn2 = self.conv_spatial(attn1)  # conv_3*3 -> conv_5*5

        attn1 = self.conv1(attn1)  # b, dim/2, h, w
        attn2 = self.conv2(attn2)  # b, dim/2, h, w

        attn = torch.cat([attn1, attn2], dim=1)  # b,c,h,w
        avg_attn = torch.mean(attn, dim=1, keepdim=True)  # b,1,h,w
        max_attn, _ = torch.max(attn, dim=1, keepdim=True)  # b,1,h,w
        agg = torch.cat([avg_attn, max_attn], dim=1)  # spa b,2,h,w

        ch_attn1 = self.global_pool(attn)  # b,dim,1, 1
        z = self.fc1(ch_attn1)
        a_b = self.fc2(z)
        a_b = a_b.reshape(batch_size, 2, dim // 2, -1)
        a_b = self.softmax(a_b)
        # 哔哩哔哩：CV缝合救星
        a1, a2 = a_b.chunk(2, dim=1)
        a1 = a1.reshape(batch_size, dim // 2, 1, 1)
        a2 = a2.reshape(batch_size, dim // 2, 1, 1)

        w1 = a1 * agg[:, 0, :, :].unsqueeze(1)
        w2 = a2 * agg[:, 0, :, :].unsqueeze(1)

        attn = attn1 * w1 + attn2 * w2
        attn = self.conv(attn).sigmoid()

        return x * attn # 哔哩哔哩：CV缝合救星


if __name__ == '__main__':
    # 参数设置
    batch_size = 2  # 批量大小, 用到了BatchNorm2d, 尽量保证batch_size大于2
    dim = 32  # 输入通道数
    height, width = 256, 256  # 输入图像的高度和宽度

    # 创建随机输入张量，形状为 (batch_size, dim, height, width)
    x = torch.randn(batch_size, dim, height, width)

    # 创建 KernelSelectiveFusionAttention 模型
    model = KernelSelectiveFusionAttention(dim=dim)

    # 打印模型结构
    print(model)
    print("哔哩哔哩: CV缝合救星!")

    # 进行前向传播，得到输出
    output = model(x)

    # 打印输入和输出的形状
    print(f"输入张量的形状: {x.shape}")
    print(f"输出张量的形状: {output.shape}")